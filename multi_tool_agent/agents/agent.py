#!/usr/bin/env python3
"""
ADK-compatible agent.py file for the agents directory.

This file provides the root_agent that ADK web console expects to find
in the agents directory structure.
"""

import logging
from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from . import create_orchestrator_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_root_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """
    Create the root agent for ADK web console.
    
    This creates the orchestrator agent with proper sub-agents that ADK
    web console can recognize and interact with.
    
    Args:
        model: The Gemini model to use for all agents
        
    Returns:
        LlmAgent instance configured as the root orchestrator
    """
    try:
        logger.info("Creating root agent for ADK web console")
        
        # Create the orchestrator agent with sub-agents
        orchestrator = create_orchestrator_agent(model)
        
        logger.info("Root agent created successfully")
        return orchestrator
        
    except Exception as e:
        logger.error(f"Failed to create root agent: {str(e)}")
        raise


# This is what ADK web console looks for
root_agent = create_root_agent()


def create_runner(agent: LlmAgent = None) -> Runner:
    """
    Create ADK Runner with proper session service.

    Args:
        agent: Optional agent instance. If None, uses root_agent.

    Returns:
        Runner instance ready for ADK web console
    """
    try:
        if agent is None:
            agent = root_agent
        
        # Create session service
        session_service = InMemorySessionService()
        
        # Create runner
        runner = Runner(
            agent=agent,
            session_service=session_service
        )
        
        logger.info("ADK Runner created successfully")
        return runner
        
    except Exception as e:
        logger.error(f"Failed to create ADK Runner: {str(e)}")
        raise


def main() -> Runner:
    """
    Main entry point for ADK web console.
    
    Returns:
        Runner instance for ADK web console
    """
    logger.info("Starting agents application")
    
    try:
        # Create and return the runner
        runner = create_runner()
        
        logger.info("Agents application started successfully")
        return runner
        
    except Exception as e:
        logger.error(f"Failed to start agents application: {str(e)}")
        raise


# Entry point for direct execution
if __name__ == "__main__":
    """
    Direct execution entry point for testing.
    """
    print("🚀 Starting ADK Agents Application")
    print("=" * 50)
    
    try:
        # Create runner
        runner = main()
        
        print("✅ Agents application initialized successfully")
        print("🎯 Three-agent architecture ready:")
        print("   - Orchestrator Agent (root)")
        print("   - Knowledge Agent (sub-agent)")
        print("   - Orders Agent (sub-agent)")
        print("\n🌐 Starting interactive session...")
        
        # Start interactive session for testing
        runner.run_interactive()
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        exit(1)
