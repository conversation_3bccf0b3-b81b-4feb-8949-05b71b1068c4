
"""
Knowledge Agent for semantic search queries against pgvector database.

This agent specializes in handling document and knowledge-based queries
using semantic search with embeddings stored in PostgreSQL with pgvector.
"""

import os
import logging
from typing import Dict, Any, Optional
from google.adk.agents import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from config import config

logger = logging.getLogger(__name__)


def create_knowledge_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """
    Create a Knowledge Agent as a proper ADK LlmAgent instance.

    Args:
        model: The Gemini model to use for the agent

    Returns:
        LlmAgent instance configured for knowledge operations
    """
    try:
        # Create MCP toolset for knowledge operations
        knowledge_toolset = MCPToolset(
            connection_params=StdioServerParameters(
                command='python3',
                args=[os.path.join(os.path.dirname(__file__), '..', 'mcp_tools', 'knowledge_mcp_server.py')],
            ),
            tool_filter=['semantic_search', 'get_document', 'list_documents']
        )

        # Create the ADK LlmAgent
        agent = LlmAgent(
            name="KnowledgeAgent",
            model=model,
            instruction=_get_knowledge_instruction(),
            description="Specialized agent for semantic search and document retrieval",
            tools=[knowledge_toolset]
        )

        logger.info("Knowledge Agent created successfully")
        return agent

    except Exception as e:
        logger.error(f"Failed to create Knowledge Agent: {str(e)}")
        raise


def _get_knowledge_instruction() -> str:
    """Get the system instruction for the knowledge agent."""
    return """
    You are a Knowledge Agent specialized in semantic search and document retrieval.

    Your responsibilities:
    - Handle queries about documents, information, and knowledge
    - Perform semantic search using pgvector embeddings
    - Retrieve relevant document content
    - Provide accurate and contextual responses based on document content

    Available tools:
    - semantic_search: Search for relevant documents using semantic similarity
    - get_document: Retrieve full document content by ID
    - list_documents: List available documents in the knowledge base

    Always use semantic search to find relevant information before responding.
    Provide clear, accurate answers based on the retrieved document content.
    If no relevant information is found, clearly state that.
    """


class KnowledgeAgent:
    """
    Legacy wrapper class for backward compatibility.
    Use create_knowledge_agent() function for new ADK-compatible implementations.
    """

    def __init__(self, model: str = "gemini-2.0-flash"):
        """
        Initialize the Knowledge Agent wrapper.

        Args:
            model: The Gemini model to use for the agent
        """
        self.model = model
        self.agent = create_knowledge_agent(model)

    def _setup_agent(self):
        """Legacy method - agent is now created in __init__."""
        pass
    
    def _get_system_instruction(self) -> str:
        """Legacy method - use _get_knowledge_instruction() function instead."""
        return _get_knowledge_instruction()
    
    async def ask(self, query: str) -> str:
        """
        Process a knowledge query using the agent.
        
        Args:
            query: The user's query about documents or knowledge
            
        Returns:
            The agent's response
        """
        if not self.agent:
            raise RuntimeError("Knowledge Agent not properly initialized")
        
        try:
            logger.info(f"Knowledge Agent processing query: {query}")
            
            # Use the ADK agent to process the query
            # Note: This is a simplified interface - in practice you'd use
            # the full ADK Runner and Session infrastructure
            response = await self.agent.process_query(query)
            
            logger.info("Knowledge Agent query processed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error processing knowledge query: {str(e)}")
            return f"I apologize, but I encountered an error while searching for information: {str(e)}"
    
    def get_capabilities(self) -> Dict[str, Any]:
        """
        Get information about the agent's capabilities.
        
        Returns:
            Dictionary describing the agent's capabilities
        """
        return {
            "name": "KnowledgeAgent",
            "description": "Handles semantic search and document retrieval queries",
            "capabilities": [
                "Semantic search using pgvector embeddings",
                "Document content retrieval",
                "Knowledge base exploration",
                "Contextual information extraction"
            ],
            "tools": ["semantic_search", "get_document", "list_documents"],
            "model": self.model
        }
