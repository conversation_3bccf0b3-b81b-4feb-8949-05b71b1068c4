"""
Agents module for the three-agent POC architecture.

This module contains the specialized agents:
- KnowledgeAgent: Handles semantic search queries against pgvector database
- OrdersAgent: Handles read-only queries against orders table
- OrchestratorAgent: Routes queries to appropriate specialized agents

New ADK-compatible functions:
- create_knowledge_agent: Creates proper ADK LlmAgent for knowledge operations
- create_orders_agent: Creates proper ADK LlmAgent for orders operations
- create_orchestrator_agent: Creates proper ADK LlmAgent with sub-agents
"""

from .knowledge_agent import KnowledgeAgent, create_knowledge_agent
from .orders_agent import OrdersAgent, create_orders_agent
from .orchestrator_agent import OrchestratorAgent, create_orchestrator_agent

__all__ = [
    'KnowledgeAgent', 'OrdersAgent', 'OrchestratorAgent',
    'create_knowledge_agent', 'create_orders_agent', 'create_orchestrator_agent'
]
