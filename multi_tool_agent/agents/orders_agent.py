"""
Orders Agent for read-only queries against the orders table.

This agent specializes in handling order-related queries with read-only
access to the orders database table using MCP tools.
"""

import os
import logging
from typing import Dict, Any, Optional
from google.adk.agents import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from config import config

logger = logging.getLogger(__name__)


def create_orders_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """
    Create an Orders Agent as a proper ADK LlmAgent instance.

    Args:
        model: The Gemini model to use for the agent

    Returns:
        LlmAgent instance configured for orders operations
    """
    try:
        # Create MCP toolset for orders operations
        # We'll use a custom MCP server for PostgreSQL operations
        orders_toolset = MCPToolset(
            connection_params=StdioServerParameters(
                command='python3',
                args=[os.path.join(os.path.dirname(__file__), '..', 'mcp_tools', 'orders_mcp_server_persistent.py')],
            ),
            tool_filter=['search_orders', 'get_order_details', 'get_orders_by_status', 'get_recent_orders']
        )

        # Create the ADK LlmAgent
        agent = LlmAgent(
            name="OrdersAgent",
            model=model,
            instruction=_get_orders_instruction(),
            description="Specialized agent for read-only orders queries",
            tools=[orders_toolset]
        )

        logger.info("Orders Agent created successfully")
        return agent

    except Exception as e:
        logger.error(f"Failed to create Orders Agent: {str(e)}")
        raise


def _get_orders_instruction() -> str:
    """Get the system instruction for the orders agent."""
    return """
    You are an Orders Agent specialized in handling order-related queries.

    Your responsibilities:
    - Handle queries about orders, customers, purchases, and order status
    - Search and filter orders by various criteria
    - Provide order details and summaries
    - Generate order reports and statistics

    IMPORTANT: You have READ-ONLY access to the orders database.
    You cannot create, update, or delete orders.

    Available tools:
    - search_orders: Search for orders by customer name, status, with optional limit
    - get_order_details: Get detailed information about a specific order by order number or ID
    - get_orders_by_status: Get all orders with a specific status (PENDIENTE, EN_PROCESO, COMPLETADA, CANCELADA)
    - get_recent_orders: Get the most recent orders with optional limit

    Always use the appropriate tool to retrieve order information before responding.
    Provide clear, accurate answers based on the retrieved order data.
    If asked to modify orders, explain that you only have read-only access.
    """


class OrdersAgent:
    """
    Legacy wrapper class for backward compatibility.
    Use create_orders_agent() function for new ADK-compatible implementations.
    """

    def __init__(self, model: str = "gemini-2.0-flash"):
        """
        Initialize the Orders Agent wrapper.

        Args:
            model: The Gemini model to use for the agent
        """
        self.model = model
        self.agent = create_orders_agent(model)
        self.toolbox_client = None  # For backward compatibility

    def _setup_agent(self):
        """Legacy method - agent is now created in __init__."""
        pass
    
    def _get_system_instruction(self) -> str:
        """Legacy method - use _get_orders_instruction() function instead."""
        return _get_orders_instruction()
    
    async def ask(self, query: str) -> str:
        """
        Process an orders query using the agent.
        
        Args:
            query: The user's query about orders
            
        Returns:
            The agent's response
        """
        if not self.agent:
            raise RuntimeError("Orders Agent not properly initialized")
        
        try:
            logger.info(f"Orders Agent processing query: {query}")
            
            # Use the ADK agent to process the query
            # Note: This is a simplified interface - in practice you'd use
            # the full ADK Runner and Session infrastructure
            response = await self.agent.process_query(query)
            
            logger.info("Orders Agent query processed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error processing orders query: {str(e)}")
            return f"I apologize, but I encountered an error while searching for order information: {str(e)}"
    
    def get_capabilities(self) -> Dict[str, Any]:
        """
        Get information about the agent's capabilities.
        
        Returns:
            Dictionary describing the agent's capabilities
        """
        return {
            "name": "OrdersAgent",
            "description": "Handles read-only queries against the orders table",
            "capabilities": [
                "Search orders by customer, status, date, priority",
                "Get detailed order information",
                "Generate order summaries and statistics",
                "Filter orders by various criteria",
                "Read-only access only"
            ],
            "access_level": "read-only",
            "model": self.model
        }
