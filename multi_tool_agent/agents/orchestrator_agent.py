
"""
Orchestrator Agent for routing queries to specialized agents.

This agent analyzes incoming queries and routes them to the appropriate
specialized agent (Knowledge Agent or Orders Agent) based on query intent.
"""

import logging
from typing import Dict, Any, Optional
from google.adk.agents import LlmAgent
from .knowledge_agent import create_knowledge_agent, KnowledgeAgent
from .orders_agent import create_orders_agent, OrdersAgent

logger = logging.getLogger(__name__)


def create_orchestrator_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """
    Create an Orchestrator Agent as a proper ADK LlmAgent with sub-agents.

    Args:
        model: The Gemini model to use for the agent

    Returns:
        LlmAgent instance configured as orchestrator with sub-agents
    """
    try:
        # Create specialized agents as sub-agents
        knowledge_agent = create_knowledge_agent(model)
        orders_agent = create_orders_agent(model)

        # Create the orchestrator agent with sub-agents
        orchestrator = LlmAgent(
            name="OrchestratorAgent",
            model=model,
            instruction=_get_orchestrator_instruction(),
            description="Main orchestrator that routes queries to specialized agents",
            sub_agents=[knowledge_agent, orders_agent]
        )

        logger.info("Orchestrator Agent created successfully with sub-agents")
        return orchestrator

    except Exception as e:
        logger.error(f"Failed to create Orchestrator Agent: {str(e)}")
        raise


def _get_orchestrator_instruction() -> str:
    """Get the system instruction for the orchestrator agent."""
    return """
    You are an Orchestrator Agent that routes user queries to the appropriate specialized agent.

    You have access to two specialized agents:

    1. KnowledgeAgent: Transfer queries about:
       - Documents and document content
       - Information search and retrieval
       - Knowledge base queries
       - Semantic search
       - General information requests

    2. OrdersAgent: Transfer queries about:
       - Orders and order status
       - Customer information
       - Purchase history
       - Order management
       - Order statistics and reports

    Your job is to:
    1. Analyze the user's query to determine the intent
    2. Transfer the query to the appropriate specialized agent using agent transfer
    3. Return the response from the specialized agent

    Guidelines for routing:
    - If the query mentions orders, customers, purchases, order status, or order-related terms → transfer to OrdersAgent
    - If the query asks for documents, information, knowledge, or general search → transfer to KnowledgeAgent
    - If unclear, consider the primary intent of the query

    Always transfer the query to exactly one of the specialized agents.
    Do not try to answer queries directly - always delegate using agent transfer.
    """


class OrchestratorAgent:
    """
    Legacy wrapper class for backward compatibility.
    Use create_orchestrator_agent() function for new ADK-compatible implementations.
    """

    def __init__(self, model: str = "gemini-2.0-flash"):
        """
        Initialize the Orchestrator Agent wrapper.

        Args:
            model: The Gemini model to use for the agent
        """
        self.model = model
        self.agent = create_orchestrator_agent(model)
        self.knowledge_agent = None  # For backward compatibility
        self.orders_agent = None     # For backward compatibility

    def _setup_agents(self):
        """Legacy method - agent is now created in __init__."""
        pass

    def _get_system_instruction(self) -> str:
        """Legacy method - use _get_orchestrator_instruction() function instead."""
        return _get_orchestrator_instruction()

    async def ask(self, query: str) -> str:
        """
        Process a query by routing it to the appropriate specialized agent.

        Args:
            query: The user's query

        Returns:
            The response from the appropriate specialized agent
        """
        if not self.agent:
            raise RuntimeError("Orchestrator Agent not properly initialized")

        try:
            logger.info(f"Orchestrator Agent processing query: {query}")

            # Use the ADK agent to process the query
            # Note: This is a simplified interface - in practice you'd use
            # the full ADK Runner and Session infrastructure
            response = await self.agent.process_query(query)

            logger.info("Orchestrator Agent query processed successfully")
            return response

        except Exception as e:
            logger.error(f"Error processing orchestrator query: {str(e)}")
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Get information about the orchestrator's capabilities.

        Returns:
            Dictionary describing the orchestrator's capabilities
        """
        return {
            "name": "OrchestratorAgent",
            "description": "Routes queries to appropriate specialized agents",
            "specialized_agents": {
                "KnowledgeAgent": "Handles semantic search and document retrieval",
                "OrdersAgent": "Handles read-only order queries"
            },
            "routing_logic": "Intent-based routing using LLM analysis with agent transfer",
            "model": self.model
        }
