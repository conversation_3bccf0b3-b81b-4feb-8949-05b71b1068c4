#!/usr/bin/env python3
"""
Test script to verify MCP server connectivity and three-agent system functionality.

This script tests:
1. Database import fixes in knowledge_mcp_server.py
2. MCP server connection stability
3. Agent creation and routing
4. End-to-end query processing
"""

import asyncio
import logging
import sys
import os
import subprocess
import time
from typing import Dict, Any, Optional

# Configure logging first
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from agents.orchestrator_agent import create_orchestrator_agent
    from agents.knowledge_agent import create_knowledge_agent
    from agents.orders_agent import create_orders_agent
    ADK_AVAILABLE = True
except ImportError as e:
    logger.warning(f"ADK not available, skipping agent tests: {e}")
    ADK_AVAILABLE = False

from config import config


class MCPConnectivityTester:
    """Test class for MCP server connectivity and agent functionality."""
    
    def __init__(self):
        self.test_results = {}
        self.knowledge_server_process = None
        self.orders_server_process = None
    
    async def test_database_imports(self) -> bool:
        """Test that database imports work correctly in knowledge MCP server."""
        logger.info("Testing database imports...")
        
        try:
            # Try to import the modules that were causing issues
            from database import KnowledgeBaseDB
            from embeddings import TitanEmbeddings
            
            logger.info("✅ Database imports successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database import failed: {str(e)}")
            return False
    
    def test_mcp_server_startup(self, server_script: str, server_name: str) -> bool:
        """Test that an MCP server can start without errors."""
        logger.info(f"Testing {server_name} startup...")
        
        try:
            # Start the server process
            process = subprocess.Popen(
                [sys.executable, server_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give it a moment to start
            time.sleep(2)
            
            # Check if process is still running (not crashed immediately)
            if process.poll() is None:
                logger.info(f"✅ {server_name} started successfully")
                process.terminate()
                process.wait(timeout=5)
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {server_name} failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing {server_name}: {str(e)}")
            return False
    
    async def test_agent_creation(self) -> Dict[str, bool]:
        """Test that all agents can be created successfully."""
        logger.info("Testing agent creation...")

        results = {}

        if not ADK_AVAILABLE:
            logger.warning("⚠️  ADK not available, skipping agent creation tests")
            results['knowledge_agent'] = None
            results['orders_agent'] = None
            results['orchestrator_agent'] = None
            return results

        # Test Knowledge Agent
        try:
            knowledge_agent = create_knowledge_agent()
            logger.info("✅ Knowledge Agent created successfully")
            results['knowledge_agent'] = True
        except Exception as e:
            logger.error(f"❌ Knowledge Agent creation failed: {str(e)}")
            results['knowledge_agent'] = False

        # Test Orders Agent
        try:
            orders_agent = create_orders_agent()
            logger.info("✅ Orders Agent created successfully")
            results['orders_agent'] = True
        except Exception as e:
            logger.error(f"❌ Orders Agent creation failed: {str(e)}")
            results['orders_agent'] = False

        # Test Orchestrator Agent
        try:
            orchestrator_agent = create_orchestrator_agent()
            logger.info("✅ Orchestrator Agent created successfully")
            results['orchestrator_agent'] = True
        except Exception as e:
            logger.error(f"❌ Orchestrator Agent creation failed: {str(e)}")
            results['orchestrator_agent'] = False

        return results
    
    async def test_deprecated_parameters(self) -> bool:
        """Test that deprecated StdioServerParameters have been replaced."""
        logger.info("Testing deprecated parameter replacement...")
        
        try:
            # Check if the files contain the old deprecated parameters
            knowledge_agent_file = os.path.join(os.path.dirname(__file__), 'agents', 'knowledge_agent.py')
            orders_agent_file = os.path.join(os.path.dirname(__file__), 'agents', 'orders_agent.py')
            
            deprecated_found = False
            
            for file_path in [knowledge_agent_file, orders_agent_file]:
                with open(file_path, 'r') as f:
                    content = f.read()
                    if 'StdioServerParameters' in content:
                        logger.error(f"❌ Found deprecated StdioServerParameters in {file_path}")
                        deprecated_found = True
                    elif 'StdioConnectionParams' in content:
                        logger.info(f"✅ Found updated StdioConnectionParams in {file_path}")
            
            if not deprecated_found:
                logger.info("✅ No deprecated parameters found")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking deprecated parameters: {str(e)}")
            return False
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        logger.info("🚀 Starting comprehensive MCP connectivity test...")
        logger.info("=" * 60)
        
        results = {
            'database_imports': False,
            'knowledge_server_startup': False,
            'orders_server_startup': False,
            'agent_creation': {},
            'deprecated_parameters': False,
            'overall_success': False
        }
        
        # Test 1: Database imports
        results['database_imports'] = await self.test_database_imports()
        
        # Test 2: MCP server startups
        knowledge_server_path = os.path.join(os.path.dirname(__file__), 'mcp_tools', 'knowledge_mcp_server.py')
        orders_server_path = os.path.join(os.path.dirname(__file__), 'mcp_tools', 'orders_mcp_server_persistent.py')
        
        results['knowledge_server_startup'] = self.test_mcp_server_startup(
            knowledge_server_path, "Knowledge MCP Server"
        )
        results['orders_server_startup'] = self.test_mcp_server_startup(
            orders_server_path, "Orders MCP Server"
        )
        
        # Test 3: Agent creation
        results['agent_creation'] = await self.test_agent_creation()
        
        # Test 4: Deprecated parameters
        results['deprecated_parameters'] = await self.test_deprecated_parameters()
        
        # Calculate overall success
        agent_values = [v for v in results['agent_creation'].values() if v is not None]
        all_agent_tests_passed = all(agent_values) if agent_values else True  # Skip if ADK not available

        results['overall_success'] = (
            results['database_imports'] and
            results['knowledge_server_startup'] and
            results['orders_server_startup'] and
            all_agent_tests_passed and
            results['deprecated_parameters']
        )
        
        # Print summary
        logger.info("=" * 60)
        logger.info("📊 TEST SUMMARY:")
        logger.info(f"Database Imports: {'✅' if results['database_imports'] else '❌'}")
        logger.info(f"Knowledge Server: {'✅' if results['knowledge_server_startup'] else '❌'}")
        logger.info(f"Orders Server: {'✅' if results['orders_server_startup'] else '❌'}")
        logger.info(f"Agent Creation: {'✅' if all_agent_tests_passed else '❌'}")
        logger.info(f"Parameter Updates: {'✅' if results['deprecated_parameters'] else '❌'}")
        logger.info(f"Overall Success: {'✅' if results['overall_success'] else '❌'}")
        logger.info("=" * 60)
        
        if results['overall_success']:
            logger.info("🎉 All tests passed! The three-agent ADK system should work correctly.")
        else:
            logger.error("⚠️  Some tests failed. Please review the issues above.")
        
        return results


async def main():
    """Main test function."""
    tester = MCPConnectivityTester()
    results = await tester.run_comprehensive_test()
    
    # Exit with appropriate code
    sys.exit(0 if results['overall_success'] else 1)


if __name__ == "__main__":
    asyncio.run(main())
