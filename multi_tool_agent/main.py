#!/usr/bin/env python3
"""
Aplicación Real de Tres Agentes - Conecta directamente a PostgreSQL
"""

import asyncio
import logging
import sys
import psycopg2
import psycopg2.extras
import os
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'db-posgres.cluster-c1oogsw0gozc.us-east-2.rds.amazonaws.com'),
    'database': os.getenv('DB_NAME', 'postgres'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', 'postgres'),
    'port': int(os.getenv('DB_PORT', 5432))
}


class TrulyRealKnowledgeAgent:
    """Agente de Conocimiento con datos reales de PostgreSQL."""
    
    def __init__(self):
        self.name = "KnowledgeAgent"
    
    async def ask(self, query: str) -> str:
        """Procesar consulta de conocimiento con datos reales."""
        logger.info(f"Knowledge Agent processing real query: {query}")
        
        try:
            # Connect to database
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Search documents in real database
            cursor.execute("""
                SELECT d.id, d.title, d.content, d.created_at
                FROM agente_v1.documents d
                ORDER BY d.created_at DESC
                LIMIT 10
            """)
            documents = cursor.fetchall()
            
            if documents:
                response = f"🔍 **Búsqueda en Base de Datos Real** - Encontré {len(documents)} documentos:\n\n"
                
                for doc in documents:
                    response += f"📄 **{doc['title']}**\n"
                    
                    # Show content preview
                    content_preview = doc['content'][:300] if doc['content'] else "Sin contenido"
                    if len(doc['content']) > 300:
                        content_preview += "..."
                    
                    response += f"📝 *Contenido:* {content_preview}\n"
                    response += f"📅 *Creado:* {doc['created_at'].strftime('%Y-%m-%d')}\n\n"
                
                cursor.close()
                conn.close()
                return response
            else:
                cursor.close()
                conn.close()
                return f"🔍 **Búsqueda en Base de Datos Real** - No se encontraron documentos para '{query}'."
                
        except Exception as e:
            logger.error(f"Error in real knowledge search: {str(e)}")
            return f"❌ Error al buscar en la base de datos: {str(e)}"


class TrulyRealOrdersAgent:
    """Agente de Órdenes con datos reales de PostgreSQL."""
    
    def __init__(self):
        self.name = "OrdersAgent"
    
    async def ask(self, query: str) -> str:
        """Procesar consulta de órdenes con datos reales."""
        logger.info(f"Orders Agent processing real query: {query}")
        
        try:
            # Connect to database
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            query_lower = query.lower()
            
            # Recent orders
            if "recientes" in query_lower or "recent" in query_lower or "últimas" in query_lower:
                cursor.execute("""
                    SELECT numero_orden, cliente, estado, monto_total, fecha_creacion, prioridad
                    FROM agente_v1.orders
                    ORDER BY fecha_creacion DESC
                    LIMIT 10
                """)
                orders = cursor.fetchall()
                
                if orders:
                    response = f"📦 **Órdenes Recientes** (Base de Datos Real) - {len(orders)} órdenes:\n\n"
                    
                    for order in orders:
                        status_emoji = {"PENDIENTE": "⏳", "EN_PROCESO": "🔄", "COMPLETADA": "✅", "CANCELADA": "❌"}
                        emoji = status_emoji.get(order['estado'], "📋")
                        
                        response += f"{emoji} **{order['numero_orden']}** - {order['cliente']}\n"
                        response += f"   💰 ${float(order['monto_total']):,.2f} | Estado: {order['estado']}\n"
                        response += f"   📅 {order['fecha_creacion'].strftime('%Y-%m-%d %H:%M')}\n\n"
                    
                    cursor.close()
                    conn.close()
                    return response
                else:
                    cursor.close()
                    conn.close()
                    return "📦 No se encontraron órdenes en la base de datos."
            
            # Search by specific client
            elif "empresa" in query_lower or "cliente" in query_lower:
                if "abc" in query_lower:
                    cliente_filter = "%ABC%"
                elif "techcorp" in query_lower:
                    cliente_filter = "%TechCorp%"
                elif "startup" in query_lower:
                    cliente_filter = "%Startup%"
                else:
                    cliente_filter = "%"
                
                cursor.execute("""
                    SELECT numero_orden, cliente, estado, monto_total, fecha_creacion, prioridad
                    FROM agente_v1.orders
                    WHERE cliente ILIKE %s
                    ORDER BY fecha_creacion DESC
                    LIMIT 20
                """, (cliente_filter,))
                orders = cursor.fetchall()
                
                if orders:
                    total_amount = sum(float(order['monto_total']) for order in orders)
                    response = f"📦 **Órdenes por Cliente** (Base de Datos Real) - {len(orders)} órdenes:\n\n"
                    
                    for order in orders:
                        status_emoji = {"PENDIENTE": "⏳", "EN_PROCESO": "🔄", "COMPLETADA": "✅", "CANCELADA": "❌"}
                        emoji = status_emoji.get(order['estado'], "📋")
                        
                        response += f"{emoji} **{order['numero_orden']}** - {order['cliente']}\n"
                        response += f"   💰 ${float(order['monto_total']):,.2f} | Estado: {order['estado']}\n"
                        response += f"   📅 {order['fecha_creacion'].strftime('%Y-%m-%d')}\n\n"
                    
                    response += f"💰 **Total:** ${total_amount:,.2f}"
                    cursor.close()
                    conn.close()
                    return response
                else:
                    cursor.close()
                    conn.close()
                    return f"📦 No se encontraron órdenes para el cliente especificado."
            
            # Search by order number
            elif "ord-" in query_lower:
                import re
                orden_match = re.search(r'ord-\d+-\d+', query_lower)
                if orden_match:
                    numero_orden = orden_match.group().upper()
                    cursor.execute("""
                        SELECT numero_orden, cliente, estado, monto_total, prioridad, 
                               fecha_creacion, productos
                        FROM agente_v1.orders
                        WHERE numero_orden = %s
                    """, (numero_orden,))
                    order = cursor.fetchone()
                    
                    if order:
                        status_emoji = {"PENDIENTE": "⏳", "EN_PROCESO": "🔄", "COMPLETADA": "✅", "CANCELADA": "❌"}
                        emoji = status_emoji.get(order['estado'], "📋")
                        
                        response = f"📋 **Detalles de Orden** (Base de Datos Real):\n\n"
                        response += f"{emoji} **{order['numero_orden']}**\n"
                        response += f"👤 Cliente: {order['cliente']}\n"
                        response += f"💰 Monto: ${float(order['monto_total']):,.2f}\n"
                        response += f"📊 Estado: {order['estado']}\n"
                        response += f"⚡ Prioridad: {order['prioridad']}\n"
                        response += f"📅 Creada: {order['fecha_creacion'].strftime('%Y-%m-%d %H:%M')}\n"
                        
                        if order['productos']:
                            response += f"\n📦 **Productos:** {len(order['productos'])} items\n"
                            for producto in order['productos']:
                                response += f"   • {producto.get('name', 'N/A')} - Cantidad: {producto.get('quantity', 0)} - ${producto.get('price', 0):,.2f}\n"
                        
                        cursor.close()
                        conn.close()
                        return response
                    else:
                        cursor.close()
                        conn.close()
                        return f"❌ No se encontró la orden {numero_orden} en la base de datos."
            
            # Search by status (EN_PROCESO, PENDIENTE, etc.)
            elif "en proceso" in query_lower or "en_proceso" in query_lower or "proceso" in query_lower:
                cursor.execute("""
                    SELECT numero_orden, cliente, estado, monto_total, prioridad, fecha_creacion
                    FROM agente_v1.orders
                    WHERE estado = 'EN_PROCESO'
                    ORDER BY fecha_creacion DESC
                """)
                orders = cursor.fetchall()
                
                if orders:
                    response = f"🔄 **Órdenes EN PROCESO** (Base de Datos Real) - {len(orders)} órdenes:\n\n"
                    
                    for order in orders:
                        response += f"🔄 **{order['numero_orden']}** - {order['cliente']}\n"
                        response += f"   💰 ${float(order['monto_total']):,.2f} | Prioridad: {order['prioridad']}\n"
                        response += f"   📅 {order['fecha_creacion'].strftime('%Y-%m-%d %H:%M')}\n\n"
                    
                    cursor.close()
                    conn.close()
                    return response
                else:
                    cursor.close()
                    conn.close()
                    return "🔄 No se encontraron órdenes en proceso en la base de datos."
            
            # General statistics
            elif "estadísticas" in query_lower or "resumen" in query_lower or "summary" in query_lower:
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_orders,
                        SUM(monto_total) as total_amount,
                        AVG(monto_total) as avg_amount,
                        COUNT(CASE WHEN estado = 'PENDIENTE' THEN 1 END) as pendiente,
                        COUNT(CASE WHEN estado = 'EN_PROCESO' THEN 1 END) as en_proceso,
                        COUNT(CASE WHEN estado = 'COMPLETADA' THEN 1 END) as completada,
                        COUNT(CASE WHEN estado = 'CANCELADA' THEN 1 END) as cancelada
                    FROM agente_v1.orders
                """)
                stats = cursor.fetchone()
                
                if stats and stats['total_orders'] > 0:
                    response = f"📊 **Estadísticas de Órdenes** (Base de Datos Real):\n\n"
                    response += f"📈 **Totales:**\n"
                    response += f"   • Total de Órdenes: {stats['total_orders']}\n"
                    response += f"   • Monto Total: ${float(stats['total_amount']):,.2f}\n"
                    response += f"   • Promedio por Orden: ${float(stats['avg_amount']):,.2f}\n\n"
                    
                    response += f"📊 **Por Estado:**\n"
                    response += f"   ⏳ PENDIENTE: {stats['pendiente']} órdenes\n"
                    response += f"   🔄 EN_PROCESO: {stats['en_proceso']} órdenes\n"
                    response += f"   ✅ COMPLETADA: {stats['completada']} órdenes\n"
                    response += f"   ❌ CANCELADA: {stats['cancelada']} órdenes\n"
                    
                    cursor.close()
                    conn.close()
                    return response
                else:
                    cursor.close()
                    conn.close()
                    return "📊 No hay órdenes en la base de datos para generar estadísticas."
            
            # General query
            else:
                cursor.execute("SELECT COUNT(*) as count FROM agente_v1.orders")
                count = cursor.fetchone()['count']
                cursor.close()
                conn.close()
                return f"📦 **Consulta General** - Hay {count} órdenes en la base de datos. Puedes preguntar por órdenes recientes, clientes específicos, números de orden, órdenes en proceso, o estadísticas."
                
        except Exception as e:
            logger.error(f"Error in real orders search: {str(e)}")
            return f"❌ Error al consultar órdenes: {str(e)}"


class TrulyRealOrchestratorAgent:
    """Orquestador con datos reales de PostgreSQL."""
    
    def __init__(self):
        self.name = "OrchestratorAgent"
        self.knowledge_agent = TrulyRealKnowledgeAgent()
        self.orders_agent = TrulyRealOrdersAgent()
    
    def _analyze_intent(self, query: str) -> str:
        """Analizar intención de la consulta."""
        query_lower = query.lower()
        
        orders_keywords = [
            "orden", "ordenes", "order", "orders", "cliente", "customer", 
            "compra", "purchase", "estado", "status", "ord-", "empresa",
            "recientes", "recent", "últimas", "estadísticas", "resumen",
            "proceso", "en_proceso", "pendiente", "completada", "cancelada"
        ]
        
        knowledge_keywords = [
            "documento", "documents", "información", "information", "busca", "search",
            "encuentra", "find", "academy", "academia", "tutorial", "dropi", 
            "qué es", "what is", "sobre", "about", "disponible", "available"
        ]
        
        orders_score = sum(1 for kw in orders_keywords if kw in query_lower)
        knowledge_score = sum(1 for kw in knowledge_keywords if kw in query_lower)
        
        return "orders" if orders_score > knowledge_score else "knowledge"
    
    async def ask(self, query: str) -> str:
        """Procesar consulta enrutando al agente apropiado."""
        intent = self._analyze_intent(query)
        
        if intent == "orders":
            logger.info("Routing to Real Orders Agent")
            response = await self.orders_agent.ask(query)
            return f"🎯 **[Enrutado a OrdersAgent - DATOS REALES POSTGRESQL]**\n\n{response}"
        else:
            logger.info("Routing to Real Knowledge Agent")
            response = await self.knowledge_agent.ask(query)
            return f"🎯 **[Enrutado a KnowledgeAgent - DATOS REALES POSTGRESQL]**\n\n{response}"


async def main():
    """Función principal."""
    print("🚀 Aplicación de Tres Agentes con Datos Reales de PostgreSQL")
    print("=" * 70)
    print("🔗 Conectando directamente a PostgreSQL...")
    
    try:
        # Test database connection
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM agente_v1.orders")
        order_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM agente_v1.documents")
        doc_count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        
        print(f"✅ ¡Conectado a PostgreSQL!")
        print(f"📦 {order_count} órdenes reales en la base de datos")
        print(f"📄 {doc_count} documentos reales en la base de datos")
        
        orchestrator = TrulyRealOrchestratorAgent()
        
        print(f"\n🎯 MODO INTERACTIVO CON DATOS REALES DE POSTGRESQL")
        print("=" * 70)
        print("Escribe tus consultas. 'help' para ejemplos, 'quit' para salir\n")
        
        while True:
            try:
                query = input("🔍 Consulta: ").strip()
                
                if query.lower() == 'quit':
                    break
                elif query.lower() == 'help':
                    print("\n📚 EJEMPLOS DE CONSULTAS REALES:")
                    print("\n📦 Consultas de Órdenes:")
                    print("  - Muéstrame las órdenes recientes")
                    print("  - Busca órdenes de Empresa ABC S.A.")
                    print("  - ¿Cuál es el estado de la orden ORD-2024-005?")
                    print("  - Muéstrame órdenes en proceso")
                    print("  - Genera estadísticas de órdenes")
                    
                    print("\n🧠 Consultas de Conocimiento:")
                    print("  - Busca información sobre Dropi Academy")
                    print("  - ¿Qué documentos hay disponibles?")
                    print("  - Encuentra tutoriales")
                    print()
                    continue
                elif not query:
                    continue
                
                print(f"\n🤖 Procesando: {query}")
                response = await orchestrator.ask(query)
                print(f"\n{response}\n")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}\n")
    
    except Exception as e:
        print(f"❌ Error de conexión a la base de datos: {str(e)}")
        sys.exit(1)
    
    print("\n👋 ¡Hasta luego!")


if __name__ == "__main__":
    asyncio.run(main())