#!/usr/bin/env python3
"""
Basic test script to verify the core fixes for MCP server connectivity issues.

This script tests:
1. Database import fixes in knowledge_mcp_server.py
2. MCP server startup without crashes
3. Deprecated parameter replacement
"""

import os
import sys
import subprocess
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_database_import_fix():
    """Test that the database import issue is fixed."""
    logger.info("Testing database import fix...")
    
    # Test the import path fix by checking if the knowledge server can import modules
    knowledge_server_path = os.path.join(os.path.dirname(__file__), 'mcp_tools', 'knowledge_mcp_server.py')
    
    try:
        # Try to run the server with a quick syntax check
        result = subprocess.run(
            [sys.executable, '-m', 'py_compile', knowledge_server_path],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            logger.info("✅ Knowledge MCP server compiles successfully")
            return True
        else:
            logger.error(f"❌ Knowledge MCP server compilation failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing knowledge server compilation: {str(e)}")
        return False


def test_deprecated_parameters():
    """Test that deprecated StdioServerParameters have been replaced."""
    logger.info("Testing deprecated parameter replacement...")
    
    files_to_check = [
        os.path.join(os.path.dirname(__file__), 'agents', 'knowledge_agent.py'),
        os.path.join(os.path.dirname(__file__), 'agents', 'orders_agent.py')
    ]
    
    deprecated_found = False
    updated_found = 0
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                
                if 'StdioServerParameters' in content:
                    logger.error(f"❌ Found deprecated StdioServerParameters in {os.path.basename(file_path)}")
                    deprecated_found = True
                
                if 'StdioConnectionParams' in content:
                    logger.info(f"✅ Found updated StdioConnectionParams in {os.path.basename(file_path)}")
                    updated_found += 1
                    
        except Exception as e:
            logger.error(f"❌ Error checking {file_path}: {str(e)}")
            return False
    
    if not deprecated_found and updated_found == len(files_to_check):
        logger.info("✅ All deprecated parameters have been replaced")
        return True
    else:
        logger.error("❌ Some deprecated parameters still exist or updates missing")
        return False


def test_mcp_server_syntax():
    """Test that MCP servers have valid syntax and can be imported."""
    logger.info("Testing MCP server syntax...")
    
    servers_to_test = [
        ('knowledge_mcp_server.py', 'Knowledge MCP Server'),
        ('orders_mcp_server_persistent.py', 'Orders MCP Server (Persistent)')
    ]
    
    all_passed = True
    
    for server_file, server_name in servers_to_test:
        server_path = os.path.join(os.path.dirname(__file__), 'mcp_tools', server_file)
        
        try:
            # Test syntax compilation
            result = subprocess.run(
                [sys.executable, '-m', 'py_compile', server_path],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {server_name} syntax is valid")
            else:
                logger.error(f"❌ {server_name} syntax error: {result.stderr}")
                all_passed = False
                
        except Exception as e:
            logger.error(f"❌ Error testing {server_name}: {str(e)}")
            all_passed = False
    
    return all_passed


def test_import_path_fixes():
    """Test that import path fixes work correctly."""
    logger.info("Testing import path fixes...")
    
    # Test if we can import the modules that were causing issues
    knowledge_server_dir = os.path.join(os.path.dirname(__file__), 'mcp_tools')
    
    # Create a test script to verify imports work
    test_script = f"""
import sys
import os

# Add the parent directory to path (same as in knowledge_mcp_server.py)
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    # Try to import the modules that were failing
    import database
    import embeddings
    import config
    print("SUCCESS: All imports work correctly")
except ImportError as e:
    print(f"FAILED: Import error - {{e}}")
    sys.exit(1)
"""
    
    test_file_path = os.path.join(knowledge_server_dir, 'test_imports.py')
    
    try:
        # Write test script
        with open(test_file_path, 'w') as f:
            f.write(test_script)
        
        # Run test script
        result = subprocess.run(
            [sys.executable, test_file_path],
            capture_output=True,
            text=True,
            timeout=10,
            cwd=knowledge_server_dir
        )
        
        # Clean up test file
        os.remove(test_file_path)
        
        if result.returncode == 0 and "SUCCESS" in result.stdout:
            logger.info("✅ Import path fixes work correctly")
            return True
        else:
            logger.error(f"❌ Import path test failed: {result.stderr}")
            return False
            
    except Exception as e:
        # Clean up test file if it exists
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        logger.error(f"❌ Error testing import paths: {str(e)}")
        return False


def run_all_tests():
    """Run all basic tests and return results."""
    logger.info("🚀 Starting basic MCP connectivity fixes test...")
    logger.info("=" * 60)
    
    tests = [
        ("Database Import Fix", test_database_import_fix),
        ("Deprecated Parameters", test_deprecated_parameters),
        ("MCP Server Syntax", test_mcp_server_syntax),
        ("Import Path Fixes", test_import_path_fixes)
    ]
    
    results = {}
    all_passed = True
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
            all_passed = False
    
    # Print summary
    logger.info("=" * 60)
    logger.info("📊 TEST SUMMARY:")
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"Overall Success: {'✅' if all_passed else '❌'}")
    logger.info("=" * 60)
    
    if all_passed:
        logger.info("🎉 All basic fixes are working correctly!")
        logger.info("The MCP server connectivity issues should be resolved.")
    else:
        logger.error("⚠️  Some tests failed. Please review the issues above.")
    
    return all_passed


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
