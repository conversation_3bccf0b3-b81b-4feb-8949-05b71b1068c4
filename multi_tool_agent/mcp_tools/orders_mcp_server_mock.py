#!/usr/bin/env python3
"""
Mock MCP Server for Orders Operations (for testing without database).

This MCP server provides mock tools for orders operations
without requiring database connectivity.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

# MCP imports
from mcp.server.fastmcp import FastMCP
from mcp.server.stdio import stdio_server

logger = logging.getLogger(__name__)

# Initialize MCP server
app = FastMCP("OrdersMockMCPServer")

# Mock data for testing
MOCK_ORDERS = [
    {
        "id": 1,
        "numero_orden": "ORD-2024-001",
        "cliente": "<PERSON>",
        "estado": "COMPLETADA",
        "fecha_creacion": "2024-01-15T10:30:00",
        "monto_total": 150.50,
        "prioridad": "ALTA",
        "productos": ["Producto A", "Producto B"]
    },
    {
        "id": 2,
        "numero_orden": "ORD-2024-002",
        "cliente": "<PERSON>",
        "estado": "EN_PROCESO",
        "fecha_creacion": "2024-01-16T14:20:00",
        "monto_total": 89.99,
        "prioridad": "MEDIA",
        "productos": ["Producto C"]
    },
    {
        "id": 3,
        "numero_orden": "ORD-2024-003",
        "cliente": "Carlos López",
        "estado": "PENDIENTE",
        "fecha_creacion": "2024-01-17T09:15:00",
        "monto_total": 299.00,
        "prioridad": "BAJA",
        "productos": ["Producto D", "Producto E", "Producto F"]
    }
]


@app.tool()
async def search_orders(customer: str = None, status: str = None, limit: int = 10) -> Dict[str, Any]:
    """
    Search for orders based on various criteria like customer, status, or date range.
    
    Args:
        customer: Customer name to search for (optional)
        status: Order status to filter by (optional)
        limit: Maximum number of results to return (default 10)
    
    Returns:
        Dictionary with search results
    """
    try:
        results = MOCK_ORDERS.copy()
        
        # Filter by customer if provided
        if customer:
            results = [order for order in results if customer.lower() in order["cliente"].lower()]
        
        # Filter by status if provided
        if status:
            results = [order for order in results if order["estado"] == status]
        
        # Apply limit
        results = results[:limit]
        
        return {
            "success": True,
            "results_count": len(results),
            "results": results,
            "filters": {
                "customer": customer,
                "status": status,
                "limit": limit
            }
        }
        
    except Exception as e:
        logger.error(f"Error in search_orders: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "filters": {
                "customer": customer,
                "status": status,
                "limit": limit
            }
        }


@app.tool()
async def get_order_details(order_identifier: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific order by order number or ID.
    
    Args:
        order_identifier: Order number or ID to retrieve
    
    Returns:
        Dictionary with order information
    """
    try:
        # Search by order number or ID
        order = None
        for o in MOCK_ORDERS:
            if o["numero_orden"] == order_identifier or str(o["id"]) == order_identifier:
                order = o
                break
        
        if order:
            return {
                "success": True,
                "order": order
            }
        else:
            return {
                "success": False,
                "error": f"Order with identifier '{order_identifier}' not found"
            }
            
    except Exception as e:
        logger.error(f"Error in get_order_details: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "order_identifier": order_identifier
        }


@app.tool()
async def get_orders_by_status(status: str, limit: int = 20) -> Dict[str, Any]:
    """
    Get all orders with a specific status.
    
    Args:
        status: Order status (PENDIENTE, EN_PROCESO, COMPLETADA, CANCELADA)
        limit: Maximum number of results to return (default 20)
    
    Returns:
        Dictionary with orders list
    """
    try:
        results = [order for order in MOCK_ORDERS if order["estado"] == status]
        results = results[:limit]
        
        return {
            "success": True,
            "status_filter": status,
            "results_count": len(results),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in get_orders_by_status: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "status_filter": status
        }


@app.tool()
async def get_recent_orders(limit: int = 10) -> Dict[str, Any]:
    """
    Get the most recent orders.
    
    Args:
        limit: Maximum number of recent orders to return (default 10)
    
    Returns:
        Dictionary with recent orders
    """
    try:
        # Sort by creation date (most recent first) and apply limit
        results = sorted(MOCK_ORDERS, key=lambda x: x["fecha_creacion"], reverse=True)
        results = results[:limit]
        
        return {
            "success": True,
            "results_count": len(results),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in get_recent_orders: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


async def main():
    """Main function to run the MCP server."""
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Orders Mock MCP Server")
    
    try:
        logger.info("Mock MCP server ready (no database required)")
        
        # Run the server
        async with stdio_server() as (read_stream, write_stream):
            await app.run(read_stream, write_stream)
    except Exception as e:
        logger.error(f"Error running MCP server: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
