# MCP Toolbox configuration for Orders operations (read-only)
# This configuration defines the PostgreSQL source and read-only tools for orders

sources:
  orders-db:
    kind: postgres
    host: ${DB_HOST}
    port: ${DB_PORT}
    database: ${DB_NAME}
    user: ${DB_USER}
    password: ${DB_PASSWORD}

tools:
  search-orders:
    kind: postgres-sql
    source: orders-db
    description: Search for orders based on various criteria like customer, status, or date range.
    parameters:
      - name: customer
        type: string
        description: Customer name to search for (optional)
      - name: status
        type: string
        description: Order status to filter by (optional)
      - name: limit
        type: integer
        description: Maximum number of results to return (default 10)
    statement: |
      SELECT 
        id, numero_orden, cliente, estado, fecha_creacion, 
        monto_total, prioridad, productos
      FROM agente_v1.orders 
      WHERE 
        ($1::text IS NULL OR cliente ILIKE '%' || $1 || '%')
        AND ($2::text IS NULL OR estado::text = $2)
      ORDER BY fecha_creacion DESC
      LIMIT COALESCE($3, 10);

  get-order-details:
    kind: postgres-sql
    source: orders-db
    description: Get detailed information about a specific order by order number or ID.
    parameters:
      - name: order_identifier
        type: string
        description: Order number or ID to retrieve
    statement: |
      SELECT 
        id, numero_orden, cliente, estado, fecha_creacion, 
        monto_total, prioridad, productos, created_at, updated_at
      FROM agente_v1.orders 
      WHERE numero_orden = $1 OR id::text = $1;

  get-orders-by-status:
    kind: postgres-sql
    source: orders-db
    description: Get all orders with a specific status.
    parameters:
      - name: status
        type: string
        description: Order status (PENDIENTE, EN_PROCESO, COMPLETADA, CANCELADA)
      - name: limit
        type: integer
        description: Maximum number of results to return (default 20)
    statement: |
      SELECT 
        id, numero_orden, cliente, estado, fecha_creacion, 
        monto_total, prioridad
      FROM agente_v1.orders 
      WHERE estado::text = $1
      ORDER BY fecha_creacion DESC
      LIMIT COALESCE($2, 20);

  get-orders-by-customer:
    kind: postgres-sql
    source: orders-db
    description: Get all orders for a specific customer.
    parameters:
      - name: customer_name
        type: string
        description: Customer name to search for
      - name: limit
        type: integer
        description: Maximum number of results to return (default 20)
    statement: |
      SELECT 
        id, numero_orden, cliente, estado, fecha_creacion, 
        monto_total, prioridad, productos
      FROM agente_v1.orders 
      WHERE cliente ILIKE '%' || $1 || '%'
      ORDER BY fecha_creacion DESC
      LIMIT COALESCE($2, 20);

  get-orders-summary:
    kind: postgres-sql
    source: orders-db
    description: Get a summary of orders including counts by status and total amounts.
    parameters: []
    statement: |
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN estado = 'PENDIENTE' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN estado = 'EN_PROCESO' THEN 1 END) as processing_orders,
        COUNT(CASE WHEN estado = 'COMPLETADA' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN estado = 'CANCELADA' THEN 1 END) as cancelled_orders,
        SUM(monto_total) as total_amount,
        AVG(monto_total) as average_amount
      FROM agente_v1.orders;

  get-recent-orders:
    kind: postgres-sql
    source: orders-db
    description: Get the most recent orders.
    parameters:
      - name: limit
        type: integer
        description: Maximum number of recent orders to return (default 10)
    statement: |
      SELECT 
        id, numero_orden, cliente, estado, fecha_creacion, 
        monto_total, prioridad
      FROM agente_v1.orders 
      ORDER BY fecha_creacion DESC
      LIMIT COALESCE($1, 10);

toolsets:
  orders-readonly:
    - search-orders
    - get-order-details
    - get-orders-by-status
    - get-orders-by-customer
    - get-orders-summary
    - get-recent-orders
