#!/usr/bin/env python3
"""
Persistent MCP Server for Orders Operations (ADK-compatible).

This MCP server provides mock tools for orders operations
with persistent connections suitable for ADK integration.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

# MCP imports
from mcp.server.fastmcp import FastMCP
from mcp.server.stdio import stdio_server

logger = logging.getLogger(__name__)

# Initialize MCP server
app = FastMCP("OrdersPersistentMCPServer")

# Mock data for testing
MOCK_ORDERS = [
    {
        "id": 1,
        "numero_orden": "ORD-2024-001",
        "cliente": "<PERSON>",
        "estado": "COMPLETADA",
        "fecha_creacion": "2024-01-15T10:30:00",
        "monto_total": 150.50,
        "prioridad": "ALTA",
        "productos": ["Producto A", "Producto B"]
    },
    {
        "id": 2,
        "numero_orden": "ORD-2024-002",
        "cliente": "<PERSON>",
        "estado": "EN_PROCESO",
        "fecha_creacion": "2024-01-16T14:20:00",
        "monto_total": 89.99,
        "prioridad": "MEDIA",
        "productos": ["Producto C"]
    },
    {
        "id": 3,
        "numero_orden": "ORD-2024-003",
        "cliente": "Carlos López",
        "estado": "PENDIENTE",
        "fecha_creacion": "2024-01-17T09:15:00",
        "monto_total": 299.00,
        "prioridad": "BAJA",
        "productos": ["Producto D", "Producto E", "Producto F"]
    }
]


@app.tool()
async def search_orders(customer: str = None, status: str = None, limit: int = 10) -> Dict[str, Any]:
    """Search for orders based on various criteria."""
    try:
        results = MOCK_ORDERS.copy()
        
        if customer:
            results = [order for order in results if customer.lower() in order["cliente"].lower()]
        
        if status:
            results = [order for order in results if order["estado"] == status]
        
        results = results[:limit]
        
        return {
            "success": True,
            "results_count": len(results),
            "results": results,
            "filters": {"customer": customer, "status": status, "limit": limit}
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.tool()
async def get_order_details(order_identifier: str) -> Dict[str, Any]:
    """Get detailed information about a specific order."""
    try:
        order = None
        for o in MOCK_ORDERS:
            if o["numero_orden"] == order_identifier or str(o["id"]) == order_identifier:
                order = o
                break
        
        if order:
            return {"success": True, "order": order}
        else:
            return {"success": False, "error": f"Order '{order_identifier}' not found"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.tool()
async def get_orders_by_status(status: str, limit: int = 20) -> Dict[str, Any]:
    """Get all orders with a specific status."""
    try:
        results = [order for order in MOCK_ORDERS if order["estado"] == status]
        results = results[:limit]
        
        return {
            "success": True,
            "status_filter": status,
            "results_count": len(results),
            "results": results
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.tool()
async def get_recent_orders(limit: int = 10) -> Dict[str, Any]:
    """Get the most recent orders."""
    try:
        results = sorted(MOCK_ORDERS, key=lambda x: x["fecha_creacion"], reverse=True)
        results = results[:limit]
        
        return {
            "success": True,
            "results_count": len(results),
            "results": results
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}


async def main():
    """Main function to run the MCP server with persistent connection handling."""
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Orders Persistent MCP Server")
    
    try:
        logger.info("Persistent MCP server ready for ADK integration")
        
        # Keep the server running with proper connection handling
        async with stdio_server() as (read_stream, write_stream):
            try:
                await app.run(read_stream, write_stream)
            except Exception as e:
                logger.info(f"MCP server session ended: {str(e)}")
                # Don't exit on connection close - this is normal for ADK
                
    except Exception as e:
        logger.error(f"Error running MCP server: {str(e)}")
        # Don't exit with error code for ADK compatibility
        pass


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("MCP server stopped by user")
    except Exception as e:
        logger.info(f"MCP server ended: {str(e)}")
        # Exit gracefully for ADK compatibility
