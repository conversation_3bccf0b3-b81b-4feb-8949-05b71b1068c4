#!/usr/bin/env python3
"""
Custom MCP Server for Knowledge Operations with pgvector.

This MCP server provides tools for semantic search and document operations
using PostgreSQL with pgvector extension.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

# MCP imports
from mcp.server.fastmcp import FastMCP
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# Local imports - use proper path resolution
import os
import sys

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from database import KnowledgeBaseDB
from embeddings import TitanEmbeddings
from config import config

logger = logging.getLogger(__name__)

# Initialize MCP server
app = FastMCP("KnowledgeMCPServer")

# Global instances
_db = None
_embeddings_client = None


async def get_db():
    """Get or create database connection with error handling."""
    global _db
    if _db is None:
        try:
            _db = KnowledgeBaseDB()
            await _db.initialize()
            logger.info("Database connection initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {str(e)}")
            raise
    return _db


async def get_embeddings_client():
    """Get or create embeddings client with error handling."""
    global _embeddings_client
    if _embeddings_client is None:
        try:
            _embeddings_client = TitanEmbeddings()
            logger.info("Embeddings client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize embeddings client: {str(e)}")
            raise
    return _embeddings_client


@app.tool()
async def semantic_search(query: str, limit: int = 5, threshold: float = 0.5) -> Dict[str, Any]:
    """
    Perform semantic search against the knowledge base.
    
    Args:
        query: The search query
        limit: Maximum number of results to return
        threshold: Minimum similarity threshold (0-1)
    
    Returns:
        Dictionary with search results
    """
    try:
        logger.info(f"Performing semantic search for query: {query}")
        db = await get_db()
        embeddings_client = await get_embeddings_client()

        # Generate embedding for the query
        logger.debug("Generating embedding for query")
        query_embedding = embeddings_client.generate_embedding(query)

        # Perform semantic search
        logger.debug(f"Searching with limit={limit}, threshold={threshold}")
        results = await db.semantic_search(query_embedding, limit=limit, threshold=threshold)

        logger.info(f"Semantic search completed, found {len(results)} results")
        return {
            "success": True,
            "query": query,
            "results_count": len(results),
            "results": [
                {
                    "document_id": result["document_id"],
                    "title": result["title"],
                    "content_snippet": result["chunk_text"][:200] + "..." if len(result["chunk_text"]) > 200 else result["chunk_text"],
                    "similarity_score": float(result["similarity"]),
                    "chunk_index": result["chunk_index"]
                }
                for result in results
            ]
        }

    except Exception as e:
        logger.error(f"Error in semantic_search: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "query": query
        }


@app.tool()
async def get_document(document_id: int) -> Dict[str, Any]:
    """
    Get full document content by ID.
    
    Args:
        document_id: The ID of the document to retrieve
    
    Returns:
        Dictionary with document information
    """
    try:
        db = await get_db()
        
        # Get document by ID
        document = await db.get_document_by_id(document_id)
        
        if document:
            return {
                "success": True,
                "document": {
                    "id": document["id"],
                    "title": document["title"],
                    "content": document["content"],
                    "metadata": document["metadata"],
                    "created_at": document["created_at"].isoformat() if document["created_at"] else None,
                    "updated_at": document["updated_at"].isoformat() if document["updated_at"] else None
                }
            }
        else:
            return {
                "success": False,
                "error": f"Document with ID {document_id} not found"
            }
            
    except Exception as e:
        logger.error(f"Error in get_document: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "document_id": document_id
        }


@app.tool()
async def list_documents(limit: int = 10, offset: int = 0) -> Dict[str, Any]:
    """
    List available documents in the knowledge base.
    
    Args:
        limit: Maximum number of documents to return
        offset: Number of documents to skip
    
    Returns:
        Dictionary with document list
    """
    try:
        db = await get_db()
        
        # Get list of documents
        documents = await db.list_documents(limit=limit, offset=offset)
        
        return {
            "success": True,
            "documents_count": len(documents),
            "limit": limit,
            "offset": offset,
            "documents": [
                {
                    "id": doc["id"],
                    "title": doc["title"],
                    "content_preview": doc["content"][:100] + "..." if len(doc["content"]) > 100 else doc["content"],
                    "created_at": doc["created_at"].isoformat() if doc["created_at"] else None
                }
                for doc in documents
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in list_documents: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


async def main():
    """Main function to run the MCP server with persistent connection handling."""
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Knowledge MCP Server")

    # Retry mechanism for server startup
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            logger.info(f"Knowledge MCP server ready for ADK integration (attempt {retry_count + 1})")

            # Keep the server running with proper connection handling
            async with stdio_server() as (read_stream, write_stream):
                try:
                    await app.run(read_stream, write_stream)
                except Exception as e:
                    logger.info(f"MCP server session ended: {str(e)}")
                    # Don't exit on connection close - this is normal for ADK
                    break

        except Exception as e:
            retry_count += 1
            logger.error(f"Error running MCP server (attempt {retry_count}): {str(e)}")
            if retry_count >= max_retries:
                logger.error("Max retries reached, server will exit")
                # Don't exit with error code for ADK compatibility
                break
            else:
                logger.info(f"Retrying in 2 seconds...")
                await asyncio.sleep(2)


if __name__ == "__main__":
    asyncio.run(main())
