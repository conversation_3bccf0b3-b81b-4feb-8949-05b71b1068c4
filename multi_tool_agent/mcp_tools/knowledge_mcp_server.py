#!/usr/bin/env python3
"""
Custom MCP Server for Knowledge Operations with pgvector.

This MCP server provides tools for semantic search and document operations
using PostgreSQL with pgvector extension.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

# MCP imports
from mcp.server.fastmcp import FastMCP
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# Local imports
sys.path.append('..')
from database import KnowledgeBaseDB
from embeddings import TitanEmbeddings
from config import config

logger = logging.getLogger(__name__)

# Initialize MCP server
app = FastMCP("KnowledgeMCPServer")

# Global instances
_db = None
_embeddings_client = None


async def get_db():
    """Get or create database connection."""
    global _db
    if _db is None:
        _db = KnowledgeBaseDB()
        await _db.initialize()
    return _db


async def get_embeddings_client():
    """Get or create embeddings client."""
    global _embeddings_client
    if _embeddings_client is None:
        _embeddings_client = TitanEmbeddings()
    return _embeddings_client


@app.tool()
async def semantic_search(query: str, limit: int = 5, threshold: float = 0.5) -> Dict[str, Any]:
    """
    Perform semantic search against the knowledge base.
    
    Args:
        query: The search query
        limit: Maximum number of results to return
        threshold: Minimum similarity threshold (0-1)
    
    Returns:
        Dictionary with search results
    """
    try:
        db = await get_db()
        embeddings_client = await get_embeddings_client()
        
        # Generate embedding for the query
        query_embedding = embeddings_client.generate_embedding(query)
        
        # Perform semantic search
        results = await db.semantic_search(query_embedding, limit=limit, threshold=threshold)
        
        return {
            "success": True,
            "query": query,
            "results_count": len(results),
            "results": [
                {
                    "document_id": result["document_id"],
                    "title": result["title"],
                    "content_snippet": result["chunk_text"][:200] + "..." if len(result["chunk_text"]) > 200 else result["chunk_text"],
                    "similarity_score": float(result["similarity"]),
                    "chunk_index": result["chunk_index"]
                }
                for result in results
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in semantic_search: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "query": query
        }


@app.tool()
async def get_document(document_id: int) -> Dict[str, Any]:
    """
    Get full document content by ID.
    
    Args:
        document_id: The ID of the document to retrieve
    
    Returns:
        Dictionary with document information
    """
    try:
        db = await get_db()
        
        # Get document by ID
        document = await db.get_document_by_id(document_id)
        
        if document:
            return {
                "success": True,
                "document": {
                    "id": document["id"],
                    "title": document["title"],
                    "content": document["content"],
                    "metadata": document["metadata"],
                    "created_at": document["created_at"].isoformat() if document["created_at"] else None,
                    "updated_at": document["updated_at"].isoformat() if document["updated_at"] else None
                }
            }
        else:
            return {
                "success": False,
                "error": f"Document with ID {document_id} not found"
            }
            
    except Exception as e:
        logger.error(f"Error in get_document: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "document_id": document_id
        }


@app.tool()
async def list_documents(limit: int = 10, offset: int = 0) -> Dict[str, Any]:
    """
    List available documents in the knowledge base.
    
    Args:
        limit: Maximum number of documents to return
        offset: Number of documents to skip
    
    Returns:
        Dictionary with document list
    """
    try:
        db = await get_db()
        
        # Get list of documents
        documents = await db.list_documents(limit=limit, offset=offset)
        
        return {
            "success": True,
            "documents_count": len(documents),
            "limit": limit,
            "offset": offset,
            "documents": [
                {
                    "id": doc["id"],
                    "title": doc["title"],
                    "content_preview": doc["content"][:100] + "..." if len(doc["content"]) > 100 else doc["content"],
                    "created_at": doc["created_at"].isoformat() if doc["created_at"] else None
                }
                for doc in documents
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in list_documents: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


async def main():
    """Main function to run the MCP server."""
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Knowledge MCP Server")
    
    try:
        # Run the server
        async with stdio_server() as (read_stream, write_stream):
            await app.run(read_stream, write_stream)
    except Exception as e:
        logger.error(f"Error running MCP server: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
