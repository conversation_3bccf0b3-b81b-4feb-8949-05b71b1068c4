#!/usr/bin/env python3
"""
Custom MCP Server for Orders Operations with PostgreSQL.

This MCP server provides tools for read-only orders operations
using PostgreSQL database queries.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional
import os

# MCP imports
from mcp.server.fastmcp import FastMCP
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# Database imports
import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Initialize MCP server
app = FastMCP("OrdersMCPServer")

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'db-posgres.cluster-c1oogsw0gozc.us-east-2.rds.amazonaws.com'),
    'database': os.getenv('DB_NAME', 'postgres'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', 'postgres'),
    'port': int(os.getenv('DB_PORT', 5432))
}

# Global database connection pool
_db_pool = None


async def get_db_pool():
    """Get or create database connection pool."""
    global _db_pool
    if _db_pool is None:
        try:
            _db_pool = await asyncpg.create_pool(
                host=DB_CONFIG['host'],
                database=DB_CONFIG['database'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                port=DB_CONFIG['port'],
                min_size=1,
                max_size=5
            )
            logger.info("Database connection pool created successfully")
        except Exception as e:
            logger.error(f"Failed to create database pool: {str(e)}")
            raise
    return _db_pool


@app.tool()
async def search_orders(customer: str = None, status: str = None, limit: int = 10) -> Dict[str, Any]:
    """
    Search for orders based on various criteria like customer, status, or date range.
    
    Args:
        customer: Customer name to search for (optional)
        status: Order status to filter by (optional)
        limit: Maximum number of results to return (default 10)
    
    Returns:
        Dictionary with search results
    """
    try:
        pool = await get_db_pool()
        
        query = """
        SELECT 
            id, numero_orden, cliente, estado, fecha_creacion, 
            monto_total, prioridad, productos
        FROM agente_v1.orders 
        WHERE 
            ($1::text IS NULL OR cliente ILIKE '%' || $1 || '%')
            AND ($2::text IS NULL OR estado::text = $2)
        ORDER BY fecha_creacion DESC
        LIMIT $3;
        """
        
        async with pool.acquire() as conn:
            rows = await conn.fetch(query, customer, status, limit)
            
            results = []
            for row in rows:
                results.append({
                    "id": row["id"],
                    "numero_orden": row["numero_orden"],
                    "cliente": row["cliente"],
                    "estado": row["estado"],
                    "fecha_creacion": row["fecha_creacion"].isoformat() if row["fecha_creacion"] else None,
                    "monto_total": float(row["monto_total"]) if row["monto_total"] else None,
                    "prioridad": row["prioridad"],
                    "productos": row["productos"]
                })
            
            return {
                "success": True,
                "results_count": len(results),
                "results": results,
                "filters": {
                    "customer": customer,
                    "status": status,
                    "limit": limit
                }
            }
            
    except Exception as e:
        logger.error(f"Error in search_orders: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "filters": {
                "customer": customer,
                "status": status,
                "limit": limit
            }
        }


@app.tool()
async def get_order_details(order_identifier: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific order by order number or ID.
    
    Args:
        order_identifier: Order number or ID to retrieve
    
    Returns:
        Dictionary with order information
    """
    try:
        pool = await get_db_pool()
        
        query = """
        SELECT 
            id, numero_orden, cliente, estado, fecha_creacion, 
            monto_total, prioridad, productos, created_at, updated_at
        FROM agente_v1.orders 
        WHERE numero_orden = $1 OR id::text = $1;
        """
        
        async with pool.acquire() as conn:
            row = await conn.fetchrow(query, order_identifier)
            
            if row:
                return {
                    "success": True,
                    "order": {
                        "id": row["id"],
                        "numero_orden": row["numero_orden"],
                        "cliente": row["cliente"],
                        "estado": row["estado"],
                        "fecha_creacion": row["fecha_creacion"].isoformat() if row["fecha_creacion"] else None,
                        "monto_total": float(row["monto_total"]) if row["monto_total"] else None,
                        "prioridad": row["prioridad"],
                        "productos": row["productos"],
                        "created_at": row["created_at"].isoformat() if row["created_at"] else None,
                        "updated_at": row["updated_at"].isoformat() if row["updated_at"] else None
                    }
                }
            else:
                return {
                    "success": False,
                    "error": f"Order with identifier '{order_identifier}' not found"
                }
                
    except Exception as e:
        logger.error(f"Error in get_order_details: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "order_identifier": order_identifier
        }


@app.tool()
async def get_orders_by_status(status: str, limit: int = 20) -> Dict[str, Any]:
    """
    Get all orders with a specific status.
    
    Args:
        status: Order status (PENDIENTE, EN_PROCESO, COMPLETADA, CANCELADA)
        limit: Maximum number of results to return (default 20)
    
    Returns:
        Dictionary with orders list
    """
    try:
        pool = await get_db_pool()
        
        query = """
        SELECT 
            id, numero_orden, cliente, estado, fecha_creacion, 
            monto_total, prioridad
        FROM agente_v1.orders 
        WHERE estado::text = $1
        ORDER BY fecha_creacion DESC
        LIMIT $2;
        """
        
        async with pool.acquire() as conn:
            rows = await conn.fetch(query, status, limit)
            
            results = []
            for row in rows:
                results.append({
                    "id": row["id"],
                    "numero_orden": row["numero_orden"],
                    "cliente": row["cliente"],
                    "estado": row["estado"],
                    "fecha_creacion": row["fecha_creacion"].isoformat() if row["fecha_creacion"] else None,
                    "monto_total": float(row["monto_total"]) if row["monto_total"] else None,
                    "prioridad": row["prioridad"]
                })
            
            return {
                "success": True,
                "status_filter": status,
                "results_count": len(results),
                "results": results
            }
            
    except Exception as e:
        logger.error(f"Error in get_orders_by_status: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "status_filter": status
        }


@app.tool()
async def get_recent_orders(limit: int = 10) -> Dict[str, Any]:
    """
    Get the most recent orders.
    
    Args:
        limit: Maximum number of recent orders to return (default 10)
    
    Returns:
        Dictionary with recent orders
    """
    try:
        pool = await get_db_pool()
        
        query = """
        SELECT 
            id, numero_orden, cliente, estado, fecha_creacion, 
            monto_total, prioridad
        FROM agente_v1.orders 
        ORDER BY fecha_creacion DESC
        LIMIT $1;
        """
        
        async with pool.acquire() as conn:
            rows = await conn.fetch(query, limit)
            
            results = []
            for row in rows:
                results.append({
                    "id": row["id"],
                    "numero_orden": row["numero_orden"],
                    "cliente": row["cliente"],
                    "estado": row["estado"],
                    "fecha_creacion": row["fecha_creacion"].isoformat() if row["fecha_creacion"] else None,
                    "monto_total": float(row["monto_total"]) if row["monto_total"] else None,
                    "prioridad": row["prioridad"]
                })
            
            return {
                "success": True,
                "results_count": len(results),
                "results": results
            }
            
    except Exception as e:
        logger.error(f"Error in get_recent_orders: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


async def main():
    """Main function to run the MCP server."""
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Orders MCP Server")
    
    try:
        # Test database connection
        pool = await get_db_pool()
        async with pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
        logger.info("Database connection test successful")
        
        # Run the server
        async with stdio_server() as (read_stream, write_stream):
            await app.run(read_stream, write_stream)
    except Exception as e:
        logger.error(f"Error running MCP server: {str(e)}")
        sys.exit(1)
    finally:
        if _db_pool:
            await _db_pool.close()


if __name__ == "__main__":
    asyncio.run(main())
