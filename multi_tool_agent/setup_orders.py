"""
Script para inicializar la tabla de órdenes y insertar datos de ejemplo
"""
import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from database import KnowledgeBaseDB, OrderStatus, OrderPriority

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_sample_orders():
    """Crea órdenes de ejemplo para demostrar el sistema"""
    
    # Inicializar base de datos
    db = KnowledgeBaseDB()
    await db.initialize()
    
    # Datos de ejemplo
    sample_orders = [
        {
            "numero_orden": "ORD-2024-001",
            "cliente": "Empresa ABC S.A.",
            "productos": [
                {"nombre": "Laptop Dell XPS 13", "cantidad": 5, "precio_unitario": 1200.00},
                {"nombre": "Mouse inalámbrico", "cantidad": 5, "precio_unitario": 25.00}
            ],
            "estado": OrderStatus.PENDIENTE,
            "monto_total": 6125.00,
            "prioridad": OrderPriority.ALTA
        },
        {
            "numero_orden": "ORD-2024-002",
            "cliente": "TechCorp Ltda.",
            "productos": [
                {"nombre": "Monitor 4K 27\"", "cantidad": 10, "precio_unitario": 350.00},
                {"nombre": "Teclado mecánico", "cantidad": 10, "precio_unitario": 80.00}
            ],
            "estado": OrderStatus.EN_PROCESO,
            "monto_total": 4300.00,
            "prioridad": OrderPriority.MEDIA
        },
        {
            "numero_orden": "ORD-2024-003",
            "cliente": "Innovación Digital",
            "productos": [
                {"nombre": "Servidor HP ProLiant", "cantidad": 2, "precio_unitario": 2500.00},
                {"nombre": "Switch de red 24 puertos", "cantidad": 1, "precio_unitario": 450.00}
            ],
            "estado": OrderStatus.COMPLETADA,
            "monto_total": 5450.00,
            "prioridad": OrderPriority.URGENTE
        },
        {
            "numero_orden": "ORD-2024-004",
            "cliente": "Empresa ABC S.A.",
            "productos": [
                {"nombre": "Impresora láser", "cantidad": 3, "precio_unitario": 180.00},
                {"nombre": "Papel A4 (resma)", "cantidad": 20, "precio_unitario": 5.50}
            ],
            "estado": OrderStatus.PENDIENTE,
            "monto_total": 650.00,
            "prioridad": OrderPriority.BAJA
        },
        {
            "numero_orden": "ORD-2024-005",
            "cliente": "StartupXYZ",
            "productos": [
                {"nombre": "MacBook Pro 16\"", "cantidad": 3, "precio_unitario": 2800.00},
                {"nombre": "iPad Pro", "cantidad": 3, "precio_unitario": 1100.00}
            ],
            "estado": OrderStatus.EN_PROCESO,
            "monto_total": 11700.00,
            "prioridad": OrderPriority.ALTA
        },
        {
            "numero_orden": "ORD-2024-006",
            "cliente": "Consultores Asociados",
            "productos": [
                {"nombre": "Licencia Office 365", "cantidad": 50, "precio_unitario": 12.00},
                {"nombre": "Antivirus corporativo", "cantidad": 50, "precio_unitario": 8.00}
            ],
            "estado": OrderStatus.CANCELADA,
            "monto_total": 1000.00,
            "prioridad": OrderPriority.MEDIA
        },
        {
            "numero_orden": "ORD-2024-007",
            "cliente": "TechCorp Ltda.",
            "productos": [
                {"nombre": "Disco SSD 1TB", "cantidad": 15, "precio_unitario": 120.00},
                {"nombre": "Memoria RAM 16GB", "cantidad": 15, "precio_unitario": 85.00}
            ],
            "estado": OrderStatus.PENDIENTE,
            "monto_total": 3075.00,
            "prioridad": OrderPriority.MEDIA
        },
        {
            "numero_orden": "ORD-2024-008",
            "cliente": "Empresa ABC S.A.",
            "productos": [
                {"nombre": "Webcam HD", "cantidad": 20, "precio_unitario": 45.00},
                {"nombre": "Auriculares con micrófono", "cantidad": 20, "precio_unitario": 35.00}
            ],
            "estado": OrderStatus.COMPLETADA,
            "monto_total": 1600.00,
            "prioridad": OrderPriority.BAJA
        }
    ]
    
    # Insertar órdenes de ejemplo
    logger.info("Insertando órdenes de ejemplo...")
    
    for i, order_data in enumerate(sample_orders):
        try:
            # Ajustar fecha de creación para simular órdenes de diferentes días
            days_ago = len(sample_orders) - i - 1
            
            order_id = await db.insert_order(
                numero_orden=order_data["numero_orden"],
                cliente=order_data["cliente"],
                productos=order_data["productos"],
                estado=order_data["estado"],
                monto_total=order_data["monto_total"],
                prioridad=order_data["prioridad"]
            )
            
            logger.info(f"✅ Orden creada: {order_data['numero_orden']} (ID: {order_id})")
            
        except Exception as e:
            if "duplicate key" in str(e).lower() or "already exists" in str(e).lower():
                logger.warning(f"⚠️  Orden ya existe: {order_data['numero_orden']}")
            else:
                logger.error(f"❌ Error al crear orden {order_data['numero_orden']}: {str(e)}")
    
    # Cerrar conexión
    await db.close()
    logger.info("✅ Proceso completado")

async def demo_order_queries():
    """Demuestra consultas de órdenes usando las herramientas"""
    
    # Inicializar base de datos
    db = KnowledgeBaseDB()
    await db.initialize()
    
    logger.info("\n" + "="*60)
    logger.info("DEMOSTRACIÓN DE CONSULTAS DE ÓRDENES")
    logger.info("="*60)
    
    # 1. Órdenes pendientes
    logger.info("\n1. Órdenes pendientes:")
    pendientes = await db.search_orders(estado=OrderStatus.PENDIENTE)
    for order in pendientes:
        logger.info(f"   - {order['numero_orden']}: {order['cliente']} - ${order['monto_total']}")
    
    # 2. Órdenes de un cliente específico
    logger.info("\n2. Órdenes de 'Empresa ABC S.A.':")
    abc_orders = await db.search_orders(cliente="Empresa ABC S.A.")
    for order in abc_orders:
        logger.info(f"   - {order['numero_orden']}: {order['estado']} - ${order['monto_total']}")
    
    # 3. Órdenes con prioridad alta
    logger.info("\n3. Órdenes con prioridad alta:")
    high_priority = await db.search_orders(prioridad=OrderPriority.ALTA)
    for order in high_priority:
        logger.info(f"   - {order['numero_orden']}: {order['cliente']} - {order['estado']}")
    
    # 4. Órdenes de hoy (simulado con las más recientes)
    logger.info("\n4. Órdenes recientes:")
    recent_orders = await db.search_orders(limit=3)
    for order in recent_orders:
        fecha = order['fecha_creacion'].strftime("%Y-%m-%d %H:%M")
        logger.info(f"   - {order['numero_orden']}: {fecha} - {order['estado']}")
    
    # Cerrar conexión
    await db.close()

async def main():
    """Función principal"""
    print("🚀 Configurando sistema de órdenes...")
    print("="*50)
    
    try:
        # Crear órdenes de ejemplo
        await create_sample_orders()
        
        # Demostrar consultas
        await demo_order_queries()
        
        print("\n✅ Sistema de órdenes configurado exitosamente!")
        print("\nAhora puedes usar el agente para consultar órdenes con comandos como:")
        print("- 'Muéstrame las órdenes pendientes'")
        print("- 'Busca órdenes de Empresa ABC S.A.'")
        print("- 'Órdenes con prioridad alta'")
        print("- 'Detalles de la orden ORD-2024-001'")
        
    except Exception as e:
        logger.error(f"❌ Error en la configuración: {str(e)}")
        print("\nAsegúrate de que:")
        print("1. PostgreSQL esté ejecutándose")
        print("2. Las credenciales de base de datos sean correctas")
        print("3. El archivo .env esté configurado")

if __name__ == "__main__":
    asyncio.run(main())
