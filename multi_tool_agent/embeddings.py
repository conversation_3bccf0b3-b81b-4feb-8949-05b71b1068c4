"""
Módulo para generar embeddings usando Amazon Bedrock Titan
"""
import json
import boto3
import logging
from typing import List, Optional
from config import config

logger = logging.getLogger(__name__)

class TitanEmbeddings:
    """Clase para generar embeddings usando Amazon Bedrock Titan"""
    
    def __init__(self, model_id: Optional[str] = None, dimensions: Optional[int] = None):
        """
        Inicializa el cliente de Titan Embeddings
        
        Args:
            model_id: ID del modelo Titan (por defecto usa config.TITAN_MODEL_ID)
            dimensions: Número de dimensiones del embedding (por defecto usa config.EMBEDDING_DIMENSIONS)
        """
        self.model_id = model_id or config.TITAN_MODEL_ID
        self.dimensions = dimensions or config.EMBEDDING_DIMENSIONS
        self.accept = "application/json"
        self.content_type = "application/json"
        
        # Configurar sesión de AWS con el perfil especificado
        session = boto3.Session(profile_name=config.AWS_PROFILE)
        self.bedrock = session.client(
            service_name='bedrock-runtime',
            region_name=config.BEDROCK_REGION
        )
        
        logger.info(f"TitanEmbeddings inicializado con modelo {self.model_id} y {self.dimensions} dimensiones")
    
    def generate_embedding(self, text: str, normalize: bool = True) -> List[float]:
        """
        Genera un embedding para el texto dado
        
        Args:
            text: Texto para generar el embedding
            normalize: Si normalizar el embedding o no
            
        Returns:
            Lista de floats representando el embedding
            
        Raises:
            Exception: Si hay error al generar el embedding
        """
        try:
            body = json.dumps({
                "inputText": text,
                "dimensions": self.dimensions,
                "normalize": normalize
            })
            
            response = self.bedrock.invoke_model(
                body=body,
                modelId=self.model_id,
                accept=self.accept,
                contentType=self.content_type
            )
            
            response_body = json.loads(response.get('body').read())
            embedding = response_body['embedding']
            
            logger.debug(f"Embedding generado para texto de {len(text)} caracteres")
            return embedding
            
        except Exception as e:
            logger.error(f"Error al generar embedding: {str(e)}")
            raise Exception(f"Error al generar embedding: {str(e)}")
    
    def generate_embeddings_batch(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        """
        Genera embeddings para una lista de textos
        
        Args:
            texts: Lista de textos para generar embeddings
            normalize: Si normalizar los embeddings o no
            
        Returns:
            Lista de embeddings
        """
        embeddings = []
        for text in texts:
            embedding = self.generate_embedding(text, normalize)
            embeddings.append(embedding)
        
        logger.info(f"Generados {len(embeddings)} embeddings en batch")
        return embeddings
    
    def __call__(self, text: str, normalize: bool = True) -> List[float]:
        """
        Permite usar la instancia como función
        
        Args:
            text: Texto para generar el embedding
            normalize: Si normalizar el embedding o no
            
        Returns:
            Lista de floats representando el embedding
        """
        return self.generate_embedding(text, normalize)
