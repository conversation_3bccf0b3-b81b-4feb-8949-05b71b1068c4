"""
Configuración para el agente ADK con PostgreSQL y Amazon Bedrock
"""
import os
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

class Config:
    """Configuración centralizada para la aplicación"""
    
    # Database Configuration
    DB_HOST = os.getenv("DB_HOST")
    DB_NAME = os.getenv("DB_NAME", "postgres")
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "postgres")
    DB_PORT = int(os.getenv("DB_PORT", "5432"))
    
    # AWS Configuration
    AWS_PROFILE = os.getenv("AWS_PROFILE", "IA")
    AWS_REGION = os.getenv("AWS_REGION", "us-east-2")
    BEDROCK_REGION = os.getenv("BEDROCK_REGION", "us-east-2")
    
    # Titan Embeddings Configuration
    TITAN_MODEL_ID = os.getenv("TITAN_MODEL_ID", "amazon.titan-embed-text-v2:0")
    EMBEDDING_DIMENSIONS = int(os.getenv("EMBEDDING_DIMENSIONS", "1024"))

    # Google AI Configuration
    GOOGLE_AI_API_KEY = os.getenv("GOOGLE_AI_API_KEY", "AIzaSyDUuidjJIN5hYDvPycVUV0_4JBEziSIgi4")
    
    @property
    def database_url(self):
        """Construye la URL de conexión a PostgreSQL"""
        return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    @property
    def async_database_url(self):
        """Construye la URL de conexión asíncrona a PostgreSQL"""
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

# Instancia global de configuración
config = Config()
