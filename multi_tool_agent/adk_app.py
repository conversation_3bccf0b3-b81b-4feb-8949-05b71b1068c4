#!/usr/bin/env python3
"""
ADK-compatible entry point for the three-agent architecture.

This module provides the proper ADK entry point that the web console can discover
and use to interact with the three-agent system (Orchestrator, Knowledge, Orders).
"""

import os
import logging
from typing import Optional
from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from agents import create_orchestrator_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_main_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """
    Create the main agent for ADK web console.
    
    This creates the orchestrator agent with proper sub-agents that ADK
    web console can recognize and interact with.
    
    Args:
        model: The Gemini model to use for all agents
        
    Returns:
        LlmAgent instance configured as the main orchestrator
    """
    try:
        logger.info("Creating main agent for ADK web console")
        
        # Create the orchestrator agent with sub-agents
        orchestrator = create_orchestrator_agent(model)
        
        logger.info("Main agent created successfully")
        return orchestrator
        
    except Exception as e:
        logger.error(f"Failed to create main agent: {str(e)}")
        raise


def create_runner(agent: Optional[LlmAgent] = None) -> Runner:
    """
    Create ADK Runner with proper session service.
    
    Args:
        agent: Optional agent instance. If None, creates the main agent.
        
    Returns:
        Runner instance ready for ADK web console
    """
    try:
        if agent is None:
            agent = create_main_agent()
        
        # Create session service
        session_service = InMemorySessionService()
        
        # Create runner
        runner = Runner(
            agent=agent,
            session_service=session_service,
            app_name="MultiAgentSystem"
        )
        
        logger.info("ADK Runner created successfully")
        return runner
        
    except Exception as e:
        logger.error(f"Failed to create ADK Runner: {str(e)}")
        raise


def main() -> Runner:
    """
    Main entry point that ADK web console can discover.
    
    This function is the primary entry point for the ADK web console.
    It creates and returns a properly configured Runner instance.
    
    Returns:
        Runner instance for ADK web console
    """
    logger.info("Starting ADK application")
    
    try:
        # Create and return the runner
        runner = create_runner()
        
        logger.info("ADK application started successfully")
        return runner
        
    except Exception as e:
        logger.error(f"Failed to start ADK application: {str(e)}")
        raise


# Entry point for direct execution
if __name__ == "__main__":
    """
    Direct execution entry point for testing.
    """
    print("🚀 Starting ADK Three-Agent Application")
    print("=" * 50)
    
    try:
        # Create runner
        runner = main()
        
        print("✅ ADK application initialized successfully")
        print("🎯 Three-agent architecture ready:")
        print("   - Orchestrator Agent (main)")
        print("   - Knowledge Agent (sub-agent)")
        print("   - Orders Agent (sub-agent)")
        print("\n🌐 Starting interactive session...")
        
        # Start interactive session for testing
        runner.run_interactive()
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        exit(1)
