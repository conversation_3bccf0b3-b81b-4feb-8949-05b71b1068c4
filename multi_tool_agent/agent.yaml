# ADK Agent Configuration for Three-Agent Architecture
# This configuration helps the ADK web console discover and understand
# the multi-agent hierarchy and routing behavior.

name: "MultiAgentSystem"
description: "Three-agent architecture with intelligent query routing"
version: "1.0.0"

# Main entry point configuration
main_agent: "OrchestratorAgent"
entry_point: "adk_app:main"
module_path: "adk_app"

# Agent hierarchy definition
agents:
  OrchestratorAgent:
    type: "LlmAgent"
    model: "gemini-2.0-flash"
    description: "Main orchestrator that analyzes queries and routes to specialized agents"
    role: "orchestrator"
    capabilities:
      - "Query intent analysis"
      - "Agent routing and delegation"
      - "Response coordination"
    sub_agents:
      - "KnowledgeAgent"
      - "OrdersAgent"
    routing_strategy: "intent_based"
    
  KnowledgeAgent:
    type: "LlmAgent"
    model: "gemini-2.0-flash"
    description: "Specialized agent for semantic search and document retrieval"
    role: "specialist"
    domain: "knowledge_management"
    capabilities:
      - "Semantic search using pgvector embeddings"
      - "Document content retrieval"
      - "Knowledge base exploration"
      - "Contextual information extraction"
    tools:
      - "semantic_search"
      - "get_document"
      - "list_documents"
    data_sources:
      - "PostgreSQL with pgvector"
      - "Document embeddings"
    
  OrdersAgent:
    type: "LlmAgent"
    model: "gemini-2.0-flash"
    description: "Specialized agent for read-only order queries and management"
    role: "specialist"
    domain: "order_management"
    access_level: "read_only"
    capabilities:
      - "Order search and filtering"
      - "Customer information retrieval"
      - "Order status tracking"
      - "Order statistics and reporting"
    tools:
      - "search-orders"
      - "get-order-details"
      - "get-orders-by-status"
      - "get-orders-by-customer"
      - "get-orders-summary"
      - "get-recent-orders"
    data_sources:
      - "PostgreSQL orders table"
      - "Customer data"

# Routing configuration
routing:
  strategy: "llm_based"
  intent_keywords:
    knowledge:
      - "document"
      - "information"
      - "search"
      - "knowledge"
      - "semantic"
      - "content"
      - "tutorial"
      - "guide"
    orders:
      - "order"
      - "customer"
      - "purchase"
      - "status"
      - "payment"
      - "delivery"
      - "invoice"

# Environment configuration
environment:
  database:
    type: "postgresql"
    schema: "agente_v1"
    tables:
      - "orders"
      - "documents"
  
  models:
    primary: "gemini-2.0-flash"
    embedding: "amazon.titan-embed-text-v2:0"
  
  tools:
    mcp_enabled: true
    toolbox_enabled: true

# Web console configuration
web_console:
  title: "Three-Agent Architecture System"
  description: "Intelligent query routing between Knowledge and Orders agents"
  features:
    - "Real-time agent routing visualization"
    - "Query intent analysis"
    - "Multi-agent conversation flow"
  
# Deployment configuration
deployment:
  type: "local"
  runner_type: "interactive"
  session_service: "in_memory"
  
# Monitoring and logging
monitoring:
  log_level: "INFO"
  track_routing: true
  track_performance: true
  
# Security configuration
security:
  orders_access: "read_only"
  data_isolation: true
  query_validation: true
