"""
Módulo para manejo de base de datos PostgreSQL con embeddings usando SQLAlchemy ORM
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json

from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, ForeignKey, Index, Enum, Numeric
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship, selectinload
from sqlalchemy import select, text
from sqlalchemy.sql import func
import pgvector.sqlalchemy
import enum

from config import config

logger = logging.getLogger(__name__)

# Base para los modelos ORM
Base = declarative_base()

# Enums para la tabla de órdenes
class OrderStatus(enum.Enum):
    """Estados posibles de una orden"""
    PENDIENTE = "pendiente"
    EN_PROCESO = "en_proceso"
    COMPLETADA = "completada"
    CANCELADA = "cancelada"

class OrderPriority(enum.Enum):
    """Prioridades posibles de una orden"""
    BAJA = "baja"
    MEDIA = "media"
    ALTA = "alta"
    URGENTE = "urgente"

class Document(Base):
    """Modelo ORM para la tabla de documentos"""
    __tablename__ = 'documents'
    __table_args__ = {'schema': 'agente_v1'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    doc_metadata = Column(JSONB, default={})
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relación con embeddings
    embeddings = relationship("Embedding", back_populates="document", cascade="all, delete-orphan")

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el modelo a diccionario"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'metadata': self.doc_metadata,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

class Embedding(Base):
    """Modelo ORM para la tabla de embeddings"""
    __tablename__ = 'embeddings'
    __table_args__ = (
        Index('idx_embeddings_document_id', 'document_id'),
        Index('idx_embeddings_vector', 'embedding', postgresql_using='ivfflat',
              postgresql_ops={'embedding': 'vector_cosine_ops'}),
        {'schema': 'agente_v1'}
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    document_id = Column(Integer, ForeignKey('agente_v1.documents.id', ondelete='CASCADE'), nullable=False)
    chunk_text = Column(Text, nullable=False)
    embedding = Column(pgvector.sqlalchemy.Vector(config.EMBEDDING_DIMENSIONS), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relación con documento
    document = relationship("Document", back_populates="embeddings")

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el modelo a diccionario"""
        return {
            'id': self.id,
            'document_id': self.document_id,
            'chunk_text': self.chunk_text,
            'embedding': self.embedding,
            'chunk_index': self.chunk_index,
            'created_at': self.created_at
        }

class Order(Base):
    """Modelo ORM para la tabla de órdenes"""
    __tablename__ = 'orders'
    __table_args__ = (
        Index('idx_orders_numero_orden', 'numero_orden'),
        Index('idx_orders_cliente', 'cliente'),
        Index('idx_orders_estado', 'estado'),
        Index('idx_orders_fecha_creacion', 'fecha_creacion'),
        Index('idx_orders_prioridad', 'prioridad'),
        {'schema': 'agente_v1'}
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    numero_orden = Column(String(50), nullable=False, unique=True)
    cliente = Column(String(200), nullable=False)
    productos = Column(JSONB, nullable=False, default=[])  # Lista de productos en formato JSON
    estado = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.PENDIENTE)
    fecha_creacion = Column(DateTime, default=datetime.utcnow, nullable=False)
    monto_total = Column(Numeric(10, 2), nullable=False)  # Decimal con 2 decimales
    prioridad = Column(Enum(OrderPriority), nullable=False, default=OrderPriority.MEDIA)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el modelo a diccionario"""
        return {
            'id': self.id,
            'numero_orden': self.numero_orden,
            'cliente': self.cliente,
            'productos': self.productos,
            'estado': self.estado.value if self.estado else None,
            'fecha_creacion': self.fecha_creacion,
            'monto_total': float(self.monto_total) if self.monto_total else None,
            'prioridad': self.prioridad.value if self.prioridad else None,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

class KnowledgeBaseDB:
    """Clase para manejar la base de conocimiento en PostgreSQL usando SQLAlchemy ORM"""

    def __init__(self):
        self.engine = None
        self.async_session_maker = None

    async def initialize(self):
        """Inicializa el motor SQLAlchemy y crea las tablas necesarias"""
        try:
            # Crear motor asíncrono de SQLAlchemy
            self.engine = create_async_engine(
                config.async_database_url,
                echo=False,  # Cambiar a True para debug SQL
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True
            )

            # Crear session maker
            self.async_session_maker = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            # Crear esquema y tablas
            await self._create_schema_and_tables()
            logger.info("Base de datos inicializada correctamente con SQLAlchemy ORM")

        except Exception as e:
            logger.error(f"Error al inicializar la base de datos: {str(e)}")
            raise
    
    async def _create_schema_and_tables(self):
        """Crea el esquema y las tablas necesarias para la base de conocimiento"""
        async with self.engine.begin() as conn:
            # Habilitar extensión vector si no está habilitada
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))

            # Crear esquema si no existe
            await conn.execute(text("CREATE SCHEMA IF NOT EXISTS agente_v1;"))

            # Crear todas las tablas definidas en los modelos
            await conn.run_sync(Base.metadata.create_all)

            logger.info("Esquema y tablas creadas/verificadas correctamente")
    
    async def insert_document(self, title: str, content: str, metadata: Dict[str, Any] = None) -> int:
        """
        Inserta un nuevo documento en la base de datos usando SQLAlchemy ORM

        Args:
            title: Título del documento
            content: Contenido del documento
            metadata: Metadatos adicionales

        Returns:
            ID del documento insertado
        """
        metadata = metadata or {}

        async with self.async_session_maker() as session:
            try:
                # Crear nuevo documento
                new_document = Document(
                    title=title,
                    content=content,
                    doc_metadata=metadata
                )

                session.add(new_document)
                await session.commit()
                await session.refresh(new_document)

                document_id = new_document.id
                logger.info(f"Documento insertado con ID: {document_id}")
                return document_id

            except Exception as e:
                await session.rollback()
                logger.error(f"Error al insertar documento: {str(e)}")
                raise
    
    async def insert_embedding(self, document_id: int, chunk_text: str,
                             embedding: List[float], chunk_index: int):
        """
        Inserta un embedding en la base de datos usando SQLAlchemy ORM

        Args:
            document_id: ID del documento
            chunk_text: Texto del chunk
            embedding: Vector de embedding
            chunk_index: Índice del chunk en el documento
        """
        async with self.async_session_maker() as session:
            try:
                # Crear nuevo embedding
                new_embedding = Embedding(
                    document_id=document_id,
                    chunk_text=chunk_text,
                    embedding=embedding,
                    chunk_index=chunk_index
                )

                session.add(new_embedding)
                await session.commit()

                logger.debug(f"Embedding insertado para documento {document_id}, chunk {chunk_index}")

            except Exception as e:
                await session.rollback()
                logger.error(f"Error al insertar embedding: {str(e)}")
                raise
    
    async def search_similar(self, query_embedding: List[float],
                           limit: int = 5, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        Busca documentos similares usando búsqueda vectorial con SQLAlchemy ORM

        Args:
            query_embedding: Embedding de la consulta
            limit: Número máximo de resultados
            threshold: Umbral de similitud (0-1)

        Returns:
            Lista de documentos similares con scores
        """
        async with self.async_session_maker() as session:
            try:
                # Construir consulta usando SQLAlchemy con operadores de pgvector
                similarity_score = (1 - Embedding.embedding.cosine_distance(query_embedding)).label('similarity_score')

                query = (
                    select(
                        Embedding.chunk_text,
                        Document.title,
                        Document.content,
                        Document.doc_metadata,
                        Embedding.chunk_index,
                        similarity_score
                    )
                    .join(Document, Embedding.document_id == Document.id)
                    .where(similarity_score > threshold)
                    .order_by(Embedding.embedding.cosine_distance(query_embedding))
                    .limit(limit)
                )

                result = await session.execute(query)
                rows = result.fetchall()

                # Convertir resultados a lista de diccionarios
                results = []
                for row in rows:
                    results.append({
                        'chunk_text': row.chunk_text,
                        'title': row.title,
                        'content': row.content,
                        'metadata': row.doc_metadata,
                        'chunk_index': row.chunk_index,
                        'similarity_score': float(row.similarity_score)
                    })

                logger.debug(f"Búsqueda vectorial completada: {len(results)} resultados encontrados")
                return results

            except Exception as e:
                logger.error(f"Error en búsqueda vectorial: {str(e)}")
                raise
    
    async def get_document_by_id(self, document_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtiene un documento por su ID usando SQLAlchemy ORM

        Args:
            document_id: ID del documento

        Returns:
            Diccionario con los datos del documento o None si no existe
        """
        async with self.async_session_maker() as session:
            try:
                # Buscar documento por ID
                query = select(Document).where(Document.id == document_id)
                result = await session.execute(query)
                document = result.scalar_one_or_none()

                if document:
                    return document.to_dict()
                return None

            except Exception as e:
                logger.error(f"Error al obtener documento por ID: {str(e)}")
                raise

    async def get_documents_with_embeddings(self, document_ids: List[int] = None) -> List[Dict[str, Any]]:
        """
        Obtiene documentos con sus embeddings asociados

        Args:
            document_ids: Lista de IDs de documentos (opcional, si no se proporciona obtiene todos)

        Returns:
            Lista de documentos con sus embeddings
        """
        async with self.async_session_maker() as session:
            try:
                query = select(Document).options(selectinload(Document.embeddings))

                if document_ids:
                    query = query.where(Document.id.in_(document_ids))

                result = await session.execute(query)
                documents = result.scalars().all()

                # Convertir a diccionarios incluyendo embeddings
                results = []
                for doc in documents:
                    doc_dict = doc.to_dict()
                    doc_dict['embeddings'] = [emb.to_dict() for emb in doc.embeddings]
                    results.append(doc_dict)

                return results

            except Exception as e:
                logger.error(f"Error al obtener documentos con embeddings: {str(e)}")
                raise

    # Métodos para gestión de órdenes
    async def insert_order(self, numero_orden: str, cliente: str, productos: List[Dict[str, Any]],
                          estado: OrderStatus, monto_total: float, prioridad: OrderPriority) -> int:
        """
        Inserta una nueva orden en la base de datos

        Args:
            numero_orden: Número único de la orden
            cliente: Nombre del cliente
            productos: Lista de productos en formato JSON
            estado: Estado de la orden
            monto_total: Monto total de la orden
            prioridad: Prioridad de la orden

        Returns:
            ID de la orden insertada
        """
        async with self.async_session_maker() as session:
            try:
                new_order = Order(
                    numero_orden=numero_orden,
                    cliente=cliente,
                    productos=productos,
                    estado=estado,
                    monto_total=monto_total,
                    prioridad=prioridad
                )

                session.add(new_order)
                await session.commit()
                await session.refresh(new_order)

                logger.info(f"Orden insertada con ID: {new_order.id}")
                return new_order.id

            except Exception as e:
                await session.rollback()
                logger.error(f"Error al insertar orden: {str(e)}")
                raise

    async def search_orders(self, cliente: str = None, estado: OrderStatus = None,
                           prioridad: OrderPriority = None, fecha_desde: datetime = None,
                           fecha_hasta: datetime = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Busca órdenes con filtros opcionales

        Args:
            cliente: Filtrar por cliente (búsqueda parcial)
            estado: Filtrar por estado específico
            prioridad: Filtrar por prioridad específica
            fecha_desde: Filtrar órdenes desde esta fecha
            fecha_hasta: Filtrar órdenes hasta esta fecha
            limit: Número máximo de resultados

        Returns:
            Lista de órdenes que coinciden con los filtros
        """
        async with self.async_session_maker() as session:
            try:
                query = select(Order)

                # Aplicar filtros
                if cliente:
                    query = query.where(Order.cliente.ilike(f"%{cliente}%"))

                if estado:
                    query = query.where(Order.estado == estado)

                if prioridad:
                    query = query.where(Order.prioridad == prioridad)

                if fecha_desde:
                    query = query.where(Order.fecha_creacion >= fecha_desde)

                if fecha_hasta:
                    query = query.where(Order.fecha_creacion <= fecha_hasta)

                # Ordenar por fecha de creación (más recientes primero)
                query = query.order_by(Order.fecha_creacion.desc()).limit(limit)

                result = await session.execute(query)
                orders = result.scalars().all()

                # Convertir a diccionarios
                results = [order.to_dict() for order in orders]

                logger.debug(f"Búsqueda de órdenes completada: {len(results)} resultados encontrados")
                return results

            except Exception as e:
                logger.error(f"Error en búsqueda de órdenes: {str(e)}")
                raise

    async def get_order_by_id(self, order_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtiene una orden por su ID

        Args:
            order_id: ID de la orden

        Returns:
            Diccionario con los datos de la orden o None si no existe
        """
        async with self.async_session_maker() as session:
            try:
                query = select(Order).where(Order.id == order_id)
                result = await session.execute(query)
                order = result.scalar_one_or_none()

                if order:
                    return order.to_dict()
                return None

            except Exception as e:
                logger.error(f"Error al obtener orden por ID: {str(e)}")
                raise

    async def get_order_by_numero(self, numero_orden: str) -> Optional[Dict[str, Any]]:
        """
        Obtiene una orden por su número de orden

        Args:
            numero_orden: Número de la orden

        Returns:
            Diccionario con los datos de la orden o None si no existe
        """
        async with self.async_session_maker() as session:
            try:
                query = select(Order).where(Order.numero_orden == numero_orden)
                result = await session.execute(query)
                order = result.scalar_one_or_none()

                if order:
                    return order.to_dict()
                return None

            except Exception as e:
                logger.error(f"Error al obtener orden por número: {str(e)}")
                raise

    async def update_order_status(self, order_id: int, nuevo_estado: OrderStatus) -> bool:
        """
        Actualiza el estado de una orden

        Args:
            order_id: ID de la orden
            nuevo_estado: Nuevo estado de la orden

        Returns:
            True si se actualizó correctamente, False si no se encontró la orden
        """
        async with self.async_session_maker() as session:
            try:
                query = select(Order).where(Order.id == order_id)
                result = await session.execute(query)
                order = result.scalar_one_or_none()

                if order:
                    order.estado = nuevo_estado
                    order.updated_at = datetime.utcnow()
                    await session.commit()
                    logger.info(f"Estado de orden {order_id} actualizado a {nuevo_estado.value}")
                    return True
                else:
                    logger.warning(f"Orden con ID {order_id} no encontrada")
                    return False

            except Exception as e:
                await session.rollback()
                logger.error(f"Error al actualizar estado de orden: {str(e)}")
                raise

    async def close(self):
        """Cierra el motor de SQLAlchemy"""
        if self.engine:
            await self.engine.dispose()
            logger.info("Motor SQLAlchemy cerrado")
