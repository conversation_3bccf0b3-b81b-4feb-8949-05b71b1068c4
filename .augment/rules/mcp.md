---
type: "always_apply"
---

<?xml version="1.0" encoding="UTF-8"?>
<mcp_guidelines>
    <summary>
        Always leverage the integrated MCP (Model Context Protocol) tools to optimize workflow efficiency and provide comprehensive solutions.
    </summary>
    
    <tools>
        <tool name="Playwright MCP">
            <description>Use for all web testing and interaction instead of creating separate test files or using the open-browser tool.</description>
            <commands>
                <command>browser_navigate_playwright</command>
                <command>browser_click_playwright</command>
                <command>browser_typeplaywright</command>
                <command>other browser* tools</command>
            </commands>
        </tool>
        
        <tool name="Context 7">
            <description>Use for accessing up-to-date documentation and library information when working with external APIs or frameworks.</description>
            <commands>
                <command>resolve-library-id</command>
                <command>get-library-docs</command>
            </commands>
        </tool>
        
        <tool name="Desktop Commander">
            <description>Use for file operations and system interactions instead of manual file handling.</description>
            <commands>
                <command>read_file_Desktop_Commander</command>
                <command>write_file_Desktop_Commander</command>
                <command>execute_command_Desktop_Commander</command>
                <command>other DC tools</command>
            </commands>
        </tool>
        
        <tool name="Sequential Thinking">
            <description>Use for complex problem-solving that requires multi-step analysis, planning, or when breaking down ambiguous requirements.</description>
            <commands>
                <command>sequentialthinking_Sequential_thinking</command>
            </commands>
        </tool>
        
        <tool name="Exa Search">
            <description>Use for real-time web searches and content retrieval when you need current information or documentation.</description>
            <commands>
                <command>web_search_exa_Exa_Search</command>
            </commands>
        </tool>
    </tools>
    
    <strategies>
        <title>Apply these tools strategically based on the task requirements:</title>
        <application task="Web Development/Testing">
            <instruction>Prioritize Playwright MCP for browser interactions.</instruction>
        </application>
        <application task="API Integration">
            <instruction>Use Context 7 for documentation and Desktop Commander for file operations.</instruction>
        </application>
        <application task="Complex Problem Analysis">
            <instruction>Use Sequential Thinking to break down the problem systematically.</instruction>
        </application>
        <application task="Research/Information Gathering">
            <instruction>Use Exa Search for current web content.</instruction>
        </application>
    </strategies>
    
    <best_practice>
        Always choose the most appropriate MCP tool for each specific task rather than using manual alternatives, and combine multiple tools when beneficial for comprehensive solutions.
    </best_practice>
</mcp_guidelines>